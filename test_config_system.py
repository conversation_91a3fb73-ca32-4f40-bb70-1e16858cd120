#!/usr/bin/env python
"""
Teste simples para verificar se o sistema config está funcional
"""
import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

def test_imports():
    """Testa se todos os imports funcionam"""
    print("🔍 Testando imports...")
    
    try:
        from apps.config.models import SystemConfiguration, UserActivityLog
        print("✅ Models importados")
    except Exception as e:
        print(f"❌ Erro nos models: {e}")
        return False
    
    try:
        from apps.config.forms.user_forms import UserCreateForm, UserSearchForm
        print("✅ Forms importados")
    except Exception as e:
        print(f"❌ Erro nos forms: {e}")
        return False
    
    try:
        from apps.config.services.user_management_service import UserManagementService
        from apps.config.services.system_config_service import AuditLogService
        print("✅ Services importados")
    except Exception as e:
        print(f"❌ Erro nos services: {e}")
        return False
    
    try:
        from apps.config.repositories.user_repository import DjangoUserRepository
        from apps.config.repositories.config_repository import DjangoAuditLogRepository
        print("✅ Repositories importados")
    except Exception as e:
        print(f"❌ Erro nos repositories: {e}")
        return False
    
    try:
        from apps.config.views import ConfigDashboardView, UserListView, UserCreateView
        print("✅ Views importadas")
    except Exception as e:
        print(f"❌ Erro nas views: {e}")
        return False
    
    return True

def test_models():
    """Testa se os models funcionam"""
    print("\n🔍 Testando models...")
    
    try:
        from apps.config.models import SystemConfiguration
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Testa criação de configuração
        config = SystemConfiguration.objects.create(
            key='test_config',
            value='test_value',
            description='Configuração de teste'
        )
        print(f"✅ Configuração criada: {config}")
        
        # Testa método JSON
        config.set_value_from_dict({'test': True, 'number': 42})
        data = config.get_value_as_json()
        print(f"✅ JSON funcionando: {data}")
        
        # Limpa teste
        config.delete()
        print("✅ Configuração removida")
        
        return True
    except Exception as e:
        print(f"❌ Erro nos models: {e}")
        return False

def test_services():
    """Testa se os services funcionam"""
    print("\n🔍 Testando services...")
    
    try:
        from apps.config.services.user_management_service import UserManagementService
        from apps.config.services.system_config_service import AuditLogService
        from apps.config.repositories.user_repository import DjangoUserRepository
        from apps.config.repositories.config_repository import DjangoAuditLogRepository
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Inicializa services
        audit_service = AuditLogService(DjangoAuditLogRepository())
        user_service = UserManagementService(DjangoUserRepository(), audit_service)
        
        print("✅ Services inicializados")
        
        # Testa busca de usuários
        users = user_service.list_users()
        print(f"✅ Lista de usuários: {users.count()} usuários encontrados")
        
        return True
    except Exception as e:
        print(f"❌ Erro nos services: {e}")
        return False

def test_forms():
    """Testa se os forms funcionam"""
    print("\n🔍 Testando forms...")
    
    try:
        from apps.config.forms.user_forms import UserCreateForm, UserSearchForm
        
        # Testa form de busca
        search_form = UserSearchForm()
        print("✅ Form de busca criado")
        
        # Testa form de criação
        create_form = UserCreateForm()
        print("✅ Form de criação criado")
        
        return True
    except Exception as e:
        print(f"❌ Erro nos forms: {e}")
        return False

def test_urls():
    """Testa se as URLs funcionam"""
    print("\n🔍 Testando URLs...")
    
    try:
        from django.urls import reverse
        
        # Testa URLs
        dashboard_url = reverse('config:dashboard')
        user_list_url = reverse('config:user_list')
        user_create_url = reverse('config:user_create')
        
        print(f"✅ Dashboard URL: {dashboard_url}")
        print(f"✅ User List URL: {user_list_url}")
        print(f"✅ User Create URL: {user_create_url}")
        
        return True
    except Exception as e:
        print(f"❌ Erro nas URLs: {e}")
        return False

def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes do sistema config...\n")
    
    tests = [
        test_imports,
        test_models,
        test_services,
        test_forms,
        test_urls
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            break
    
    print(f"\n📊 Resultado: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Sistema config está 100% funcional!")
        return True
    else:
        print("❌ Sistema config tem problemas")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
