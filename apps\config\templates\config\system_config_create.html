{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}

{% block config_title %}Criar Configuração{% endblock %}

{% block breadcrumbs %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'config:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'config:system_config' %}">Sistema</a></li>
            <li class="breadcrumb-item"><a href="{% url 'config:system_config_list' %}">Configurações</a></li>
            <li class="breadcrumb-item active">Criar</li>
        </ol>
    </nav>
</div>
{% endblock %}

{% block config_content %}
<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-plus me-2 text-primary"></i>Criar Nova Configuração
                </h1>
                <p class="text-muted mb-0">Adicione uma nova configuração ao sistema</p>
            </div>
            <div>
                <a href="{% url 'config:system_config_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar à Lista
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Formulário Principal -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-0">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 40px; height: 40px;">
                            <i class="fas fa-cog"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-0">Nova Configuração do Sistema</h5>
                        <small class="text-muted">Preencha os dados da configuração</small>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                {% crispy form %}
            </div>
        </div>
    </div>

    <!-- Sidebar com Dicas -->
    <div class="col-lg-4">
        <!-- Dicas de Configuração -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-info text-white border-0">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Dicas de Configuração
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-start mb-3">
                    <i class="fas fa-key text-primary me-2 mt-1"></i>
                    <div>
                        <strong>Chave Única</strong>
                        <div class="small text-muted">Use nomes descritivos como "site_name", "email_host", "api_timeout"</div>
                    </div>
                </div>
                <div class="d-flex align-items-start mb-3">
                    <i class="fas fa-code text-success me-2 mt-1"></i>
                    <div>
                        <strong>Formato JSON</strong>
                        <div class="small text-muted">Para valores complexos, use JSON válido</div>
                    </div>
                </div>
                <div class="d-flex align-items-start">
                    <i class="fas fa-info-circle text-warning me-2 mt-1"></i>
                    <div>
                        <strong>Descrição Clara</strong>
                        <div class="small text-muted">Explique o propósito e impacto da configuração</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exemplos Comuns -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light border-0">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>Exemplos Comuns
                </h6>
            </div>
            <div class="card-body">
                <div class="accordion" id="examplesAccordion">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#example1">
                                <i class="fas fa-globe me-2"></i>Configurações do Site
                            </button>
                        </h2>
                        <div id="example1" class="accordion-collapse collapse" data-bs-parent="#examplesAccordion">
                            <div class="accordion-body">
                                <div class="mb-2">
                                    <strong>site_name:</strong>
                                    <code class="small d-block">Meu Site Incrível</code>
                                </div>
                                <div class="mb-2">
                                    <strong>site_description:</strong>
                                    <code class="small d-block">O melhor site do mundo</code>
                                </div>
                                <div>
                                    <strong>maintenance_mode:</strong>
                                    <code class="small d-block">false</code>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#example2">
                                <i class="fas fa-envelope me-2"></i>Configurações de Email
                            </button>
                        </h2>
                        <div id="example2" class="accordion-collapse collapse" data-bs-parent="#examplesAccordion">
                            <div class="accordion-body">
                                <div class="mb-2">
                                    <strong>email_settings:</strong>
                                    <pre class="small bg-light p-2 rounded">{
  "host": "smtp.gmail.com",
  "port": 587,
  "use_tls": true,
  "from_email": "<EMAIL>"
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#example3">
                                <i class="fas fa-cog me-2"></i>Configurações de API
                            </button>
                        </h2>
                        <div id="example3" class="accordion-collapse collapse" data-bs-parent="#examplesAccordion">
                            <div class="accordion-body">
                                <div class="mb-2">
                                    <strong>api_timeout:</strong>
                                    <code class="small d-block">30</code>
                                </div>
                                <div class="mb-2">
                                    <strong>api_rate_limit:</strong>
                                    <code class="small d-block">1000</code>
                                </div>
                                <div>
                                    <strong>allowed_origins:</strong>
                                    <pre class="small bg-light p-2 rounded">["https://app.exemplo.com", "https://admin.exemplo.com"]</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-0">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Ações Rápidas
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'config:system_config_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-1"></i>Lista de Configurações
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="document.querySelector('form').reset()">
                        <i class="fas fa-undo me-1"></i>Limpar Formulário
                    </button>
                    <a href="{% url 'config:system_config' %}" class="btn btn-outline-info">
                        <i class="fas fa-tachometer-alt me-1"></i>Visão Geral
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.accordion-button {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--bs-primary);
    color: white;
}

.accordion-body {
    padding: 0.75rem 1rem;
}

pre {
    font-size: 0.75rem;
    margin-bottom: 0;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus no primeiro campo
    const firstInput = document.querySelector('input[type="text"]');
    if (firstInput) {
        firstInput.focus();
    }
    
    // Validação JSON em tempo real
    const valueField = document.querySelector('textarea[name="value"]');
    const valueTypeRadios = document.querySelectorAll('input[name="value_type"]');
    const jsonValidation = document.getElementById('jsonValidation');
    const jsonValidationText = document.getElementById('jsonValidationText');
    
    if (valueField && jsonValidation) {
        valueField.addEventListener('input', function() {
            const value = this.value.trim();
            const selectedType = document.querySelector('input[name="value_type"]:checked')?.value;
            
            if (selectedType === 'json' && value) {
                try {
                    JSON.parse(value);
                    jsonValidation.className = 'alert alert-success';
                    jsonValidationText.innerHTML = '<i class="fas fa-check me-1"></i>JSON válido!';
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } catch (e) {
                    jsonValidation.className = 'alert alert-danger';
                    jsonValidationText.innerHTML = '<i class="fas fa-times me-1"></i>JSON inválido: ' + e.message;
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
                jsonValidation.classList.remove('d-none');
            } else {
                jsonValidation.classList.add('d-none');
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
    }
    
    // Mudança de tipo de valor
    if (valueTypeRadios) {
        valueTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                const valueField = document.querySelector('textarea[name="value"]');
                if (valueField) {
                    // Trigger validation
                    valueField.dispatchEvent(new Event('input'));
                    
                    // Update placeholder
                    if (this.value === 'json') {
                        valueField.placeholder = 'Digite um JSON válido, ex: {"chave": "valor"}';
                    } else if (this.value === 'boolean') {
                        valueField.placeholder = 'Digite true ou false';
                    } else {
                        valueField.placeholder = 'Digite o valor da configuração';
                    }
                }
            });
        });
    }
    
    // Copiar exemplos
    document.querySelectorAll('.accordion-body code, .accordion-body pre').forEach(element => {
        element.style.cursor = 'pointer';
        element.title = 'Clique para copiar';
        
        element.addEventListener('click', function() {
            const text = this.textContent;
            navigator.clipboard.writeText(text).then(() => {
                // Feedback visual
                const original = this.style.backgroundColor;
                this.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    this.style.backgroundColor = original;
                }, 500);
            });
        });
    });
    
    // Validação do formulário
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(event) {
            const keyField = document.querySelector('input[name="key"]');
            const valueField = document.querySelector('textarea[name="value"]');
            
            // Validação da chave
            if (keyField && keyField.value) {
                const key = keyField.value.toLowerCase();
                if (!/^[a-z0-9_-]+$/.test(key)) {
                    event.preventDefault();
                    keyField.classList.add('is-invalid');
                    alert('A chave deve conter apenas letras minúsculas, números, underscore e hífen.');
                    return;
                }
            }
            
            // Validação JSON se necessário
            const selectedType = document.querySelector('input[name="value_type"]:checked')?.value;
            if (selectedType === 'json' && valueField && valueField.value) {
                try {
                    JSON.parse(valueField.value);
                } catch (e) {
                    event.preventDefault();
                    valueField.classList.add('is-invalid');
                    alert('O JSON está inválido: ' + e.message);
                    return;
                }
            }
        });
    }
});
</script>
{% endblock %}
