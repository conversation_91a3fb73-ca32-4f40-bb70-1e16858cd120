from django.urls import path
from apps.accounts.views import (
    RegistrationView,
    VerificationView,
    LoginView,
    LogoutView,
    PasswordResetRequestView,
    PasswordResetConfirmView,
    UserProfileView,
    UserUpdateView
)

app_name = 'accounts'

urlpatterns = [
    # Registro
    path('registro/', RegistrationView.as_view(), name='register'),
    path('verificacao/', VerificationView.as_view(), name='verification'),

    # Autenticação
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),

    # Redefinição de senha
    path('redefinir-senha/', PasswordResetRequestView.as_view(), name='password_reset'),
    path(
        'confirmar-senha/<slug:slug>/',
        PasswordResetConfirmView.as_view(),
        name='password_reset_confirm'
    ),

    # Perfil
    path('perfil/<slug:slug>/', UserProfileView.as_view(), name='user_profile'),
    path('perfil/<slug:slug>/editar/', UserUpdateView.as_view(), name='user_update'),
]