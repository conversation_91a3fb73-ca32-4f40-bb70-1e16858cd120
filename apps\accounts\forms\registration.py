from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError

User = get_user_model()

class RegistrationForm(UserCreationForm):
    """Formulário de registro de usuário"""

    email = forms.EmailField(
        label='E-mail',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    first_name = forms.CharField(
        label='Nome',
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Seu nome'
        })
    )
    last_name = forms.CharField(
        label='Sobrenome',
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Seu sobrenome'
        })
    )
    username = forms.Char<PERSON><PERSON>(
        label='Nome de usuário',
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'nome_usuario'
        })
    )
    password1 = forms.CharField(
        label='Senha',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite sua senha'
        })
    )
    password2 = forms.CharField(
        label='Confirmar senha',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirme sua senha'
        })
    )

    class Meta:
        model = User
        fields = ('email', 'username', 'first_name', 'last_name', 'password1', 'password2')

    def clean_email(self):
        """Valida se o email não está em uso por usuário verificado"""
        email = self.cleaned_data.get('email')
        if email:
            # Verifica se existe usuário verificado com este email
            if User.objects.filter(email__iexact=email, is_verified=True).exists():
                raise ValidationError('Já existe um usuário verificado com este e-mail.')
        return email

    def clean_username(self):
        """Valida se o username não está em uso"""
        username = self.cleaned_data.get('username')
        if username:
            if User.objects.filter(username__iexact=username).exists():
                raise ValidationError('Este nome de usuário já está em uso.')
        return username

class VerificationForm(forms.Form):
    """Formulário para verificação de código"""

    email = forms.EmailField(
        label='E-mail',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'readonly': True
        })
    )
    code = forms.CharField(
        label='Código de verificação',
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '123456',
            'maxlength': '6'
        })
    )

    def clean_code(self):
        """Valida se o código tem apenas números"""
        code = self.cleaned_data.get('code')
        if code and not code.isdigit():
            raise ValidationError('O código deve conter apenas números.')
        return code