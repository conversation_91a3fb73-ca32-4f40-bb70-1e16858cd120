{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Configurações - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">
                        <i class="fas fa-cog me-2 text-primary"></i>Configurações
                    </h1>
                    <p class="text-muted mb-0">Gerencie suas informações pessoais e configurações de conta</p>
                </div>
                <div>
                    <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Voltar ao Perfil
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar de Navegação -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="#profile-section" class="list-group-item list-group-item-action active" data-section="profile">
                            <i class="fas fa-user me-2"></i>Perfil
                        </a>
                        <a href="#avatar-section" class="list-group-item list-group-item-action" data-section="avatar">
                            <i class="fas fa-camera me-2"></i>Avatar
                        </a>
                        <a href="#email-section" class="list-group-item list-group-item-action" data-section="email">
                            <i class="fas fa-envelope me-2"></i>E-mail
                        </a>
                        <a href="#password-section" class="list-group-item list-group-item-action" data-section="password">
                            <i class="fas fa-lock me-2"></i>Senha
                        </a>
                    </div>
                </div>
            </div>

            <!-- Card de Informações do Usuário -->
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <img src="{{ profile_user.get_avatar_url }}" 
                         alt="Avatar" 
                         class="rounded-circle mb-3"
                         width="80" height="80"
                         style="object-fit: cover;">
                    <h6 class="mb-1">{{ profile_user.get_full_name|default:profile_user.username }}</h6>
                    <p class="text-muted small mb-0">{{ profile_user.email }}</p>
                </div>
            </div>
        </div>

        <!-- Conteúdo Principal -->
        <div class="col-lg-9">
            <!-- Seção de Perfil -->
            <div id="profile-section" class="settings-section">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>Informações Pessoais
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="profile">
                            {% crispy profile_form %}
                        </form>
                    </div>
                </div>
            </div>

            <!-- Seção de Avatar -->
            <div id="avatar-section" class="settings-section d-none">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-camera me-2"></i>Foto de Perfil
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="mb-3">
                                    <img src="{{ profile_user.get_avatar_url }}" 
                                         alt="Avatar atual" 
                                         class="rounded-circle border border-3 border-light shadow"
                                         width="150" height="150"
                                         style="object-fit: cover;"
                                         id="avatar-preview">
                                </div>
                                {% if profile_user.avatar %}
                                    <form method="post" action="{% url 'accounts:remove_avatar' %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                onclick="return confirm('Tem certeza que deseja remover sua foto de perfil?')">
                                            <i class="fas fa-trash me-1"></i>Remover Foto
                                        </button>
                                    </form>
                                {% endif %}
                            </div>
                            <div class="col-md-8">
                                <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                    {% csrf_token %}
                                    <input type="hidden" name="form_type" value="avatar">
                                    
                                    <div class="mb-3">
                                        <label for="id_avatar" class="form-label">Nova Foto de Perfil</label>
                                        <input type="file" class="form-control" id="id_avatar" name="avatar" accept="image/*" onchange="previewAvatar(this)">
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Formatos aceitos: JPG, PNG, GIF, WebP. Tamanho máximo: 5MB
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-upload me-2"></i>Atualizar Foto
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seção de E-mail -->
            <div id="email-section" class="settings-section d-none">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>Alterar E-mail
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="email">
                            {% crispy email_form %}
                        </form>
                    </div>
                </div>
            </div>

            <!-- Seção de Senha -->
            <div id="password-section" class="settings-section d-none">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-lock me-2"></i>Alterar Senha
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="password">
                            {% crispy password_form %}
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-section {
    transition: opacity 0.3s ease;
}

.list-group-item {
    border: none;
    padding: 12px 20px;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: #007bff;
    color: white;
    border-radius: 0;
}

.list-group-item.active:hover {
    background-color: #0056b3;
    transform: none;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

#avatar-preview {
    transition: transform 0.3s ease;
}

#avatar-preview:hover {
    transform: scale(1.05);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Navegação entre seções
    const navLinks = document.querySelectorAll('[data-section]');
    const sections = document.querySelectorAll('.settings-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active de todos os links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Adiciona active ao link clicado
            this.classList.add('active');
            
            // Esconde todas as seções
            sections.forEach(section => section.classList.add('d-none'));
            
            // Mostra a seção correspondente
            const targetSection = document.getElementById(this.dataset.section + '-section');
            if (targetSection) {
                targetSection.classList.remove('d-none');
            }
        });
    });
    
    // Verificar se há hash na URL
    const hash = window.location.hash;
    if (hash) {
        const targetLink = document.querySelector(`[href="${hash}"]`);
        if (targetLink) {
            targetLink.click();
        }
    }
});

function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            document.getElementById('avatar-preview').src = e.target.result;
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

// Validação de formulários
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
