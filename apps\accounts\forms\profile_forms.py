from django import forms
from django.contrib.auth import get_user_model
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML, Div
from crispy_forms.bootstrap import Field

User = get_user_model()

class ProfileUpdateForm(forms.ModelForm):
    """Formulário para atualização do perfil do usuário"""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'bio', 'phone', 
            'birth_date', 'location'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'placeholder': 'Digite seu nome',
                'class': 'form-control'
            }),
            'last_name': forms.TextInput(attrs={
                'placeholder': 'Digite seu sobrenome',
                'class': 'form-control'
            }),
            'bio': forms.Textarea(attrs={
                'placeholder': 'Conte um pouco sobre você...',
                'rows': 4,
                'class': 'form-control'
            }),
            'phone': forms.TextInput(attrs={
                'placeholder': '(11) 99999-9999',
                'class': 'form-control'
            }),
            'birth_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'location': forms.TextInput(attrs={
                'placeholder': 'Cidade, Estado',
                'class': 'form-control'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.attrs = {'novalidate': ''}
        
        self.helper.layout = Layout(
            Fieldset(
                '👤 Informações Pessoais',
                Row(
                    Column('first_name', css_class='form-group col-md-6 mb-3'),
                    Column('last_name', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('bio', css_class='mb-3'),
                css_class='mb-4'
            ),
            Fieldset(
                '📞 Informações de Contato',
                Row(
                    Column('phone', css_class='form-group col-md-6 mb-3'),
                    Column('location', css_class='form-group col-md-6 mb-3'),
                    css_class='form-row'
                ),
                Field('birth_date', css_class='mb-3'),
                css_class='mb-4'
            ),
            Div(
                Submit('submit', '💾 Salvar Alterações', css_class='btn btn-primary btn-lg'),
                HTML('<a href="{% url "accounts:profile" %}" class="btn btn-outline-secondary btn-lg ms-2">❌ Cancelar</a>'),
                css_class='text-center'
            )
        )
        
        # Adicionar classes de validação
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'form-control'})
            if field.required:
                field.widget.attrs.update({'required': 'required'})

class AvatarUpdateForm(forms.ModelForm):
    """Formulário para atualização do avatar do usuário"""
    
    class Meta:
        model = User
        fields = ['avatar']
        widgets = {
            'avatar': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.form_class = 'avatar-form'
        
        self.helper.layout = Layout(
            Fieldset(
                '📸 Foto de Perfil',
                HTML('''
                    <div class="text-center mb-4">
                        <div class="avatar-preview mb-3">
                            {% if user.avatar %}
                                <img src="{{ user.get_avatar_url }}" class="rounded-circle" width="150" height="150" alt="Avatar atual" id="avatar-preview">
                            {% else %}
                                <img src="{{ user.get_default_avatar }}" class="rounded-circle" width="150" height="150" alt="Avatar padrão" id="avatar-preview">
                            {% endif %}
                        </div>
                        <p class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            Formatos aceitos: JPG, PNG, GIF. Tamanho máximo: 5MB
                        </p>
                    </div>
                '''),
                Field('avatar', css_class='mb-3'),
                css_class='mb-4'
            ),
            Div(
                Submit('submit', '📸 Atualizar Foto', css_class='btn btn-success btn-lg'),
                HTML('<button type="button" class="btn btn-outline-danger btn-lg ms-2" onclick="removeAvatar()">🗑️ Remover Foto</button>'),
                css_class='text-center'
            )
        )
    
    def clean_avatar(self):
        """Validação personalizada para o avatar"""
        avatar = self.cleaned_data.get('avatar')
        
        if avatar:
            # Verificar tamanho do arquivo (5MB máximo)
            if avatar.size > 5 * 1024 * 1024:
                raise forms.ValidationError('O arquivo é muito grande. Tamanho máximo: 5MB.')
            
            # Verificar tipo do arquivo
            valid_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
            if avatar.content_type not in valid_types:
                raise forms.ValidationError('Formato de arquivo não suportado. Use JPG, PNG, GIF ou WebP.')
        
        return avatar

class EmailUpdateForm(forms.ModelForm):
    """Formulário para atualização do email do usuário"""
    
    current_password = forms.CharField(
        label='Senha Atual',
        widget=forms.PasswordInput(attrs={
            'placeholder': 'Digite sua senha atual',
            'class': 'form-control',
            'autocomplete': 'current-password'
        }),
        help_text='Por segurança, confirme sua senha atual'
    )
    
    class Meta:
        model = User
        fields = ['email']
        widgets = {
            'email': forms.EmailInput(attrs={
                'placeholder': '<EMAIL>',
                'class': 'form-control'
            })
        }
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        
        self.helper.layout = Layout(
            Fieldset(
                '📧 Alterar E-mail',
                HTML(f'''
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>E-mail atual:</strong> {user.email}
                    </div>
                '''),
                Field('email', css_class='mb-3'),
                Field('current_password', css_class='mb-3'),
                HTML('''
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Atenção:</strong> Você receberá um código de verificação no novo e-mail.
                    </div>
                '''),
                css_class='mb-4'
            ),
            Div(
                Submit('submit', '📧 Alterar E-mail', css_class='btn btn-warning btn-lg'),
                HTML('<a href="{% url "accounts:profile" %}" class="btn btn-outline-secondary btn-lg ms-2">❌ Cancelar</a>'),
                css_class='text-center'
            )
        )
    
    def clean_current_password(self):
        """Valida a senha atual"""
        password = self.cleaned_data.get('current_password')
        if not self.user.check_password(password):
            raise forms.ValidationError('Senha atual incorreta.')
        return password
    
    def clean_email(self):
        """Valida o novo email"""
        email = self.cleaned_data.get('email')
        if email == self.user.email:
            raise forms.ValidationError('O novo e-mail deve ser diferente do atual.')
        
        # Verificar se o email já está em uso
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError('Este e-mail já está sendo usado por outro usuário.')
        
        return email

class PasswordChangeForm(forms.Form):
    """Formulário para alteração de senha"""
    
    current_password = forms.CharField(
        label='Senha Atual',
        widget=forms.PasswordInput(attrs={
            'placeholder': 'Digite sua senha atual',
            'class': 'form-control',
            'autocomplete': 'current-password'
        })
    )
    new_password1 = forms.CharField(
        label='Nova Senha',
        widget=forms.PasswordInput(attrs={
            'placeholder': 'Digite a nova senha',
            'class': 'form-control',
            'autocomplete': 'new-password'
        }),
        help_text='Mínimo 8 caracteres, com letras e números'
    )
    new_password2 = forms.CharField(
        label='Confirmar Nova Senha',
        widget=forms.PasswordInput(attrs={
            'placeholder': 'Confirme a nova senha',
            'class': 'form-control',
            'autocomplete': 'new-password'
        })
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        
        self.helper.layout = Layout(
            Fieldset(
                '🔒 Alterar Senha',
                Field('current_password', css_class='mb-3'),
                Field('new_password1', css_class='mb-3'),
                Field('new_password2', css_class='mb-3'),
                css_class='mb-4'
            ),
            Div(
                Submit('submit', '🔒 Alterar Senha', css_class='btn btn-danger btn-lg'),
                HTML('<a href="{% url "accounts:profile" %}" class="btn btn-outline-secondary btn-lg ms-2">❌ Cancelar</a>'),
                css_class='text-center'
            )
        )
    
    def clean_current_password(self):
        """Valida a senha atual"""
        password = self.cleaned_data.get('current_password')
        if not self.user.check_password(password):
            raise forms.ValidationError('Senha atual incorreta.')
        return password
    
    def clean_new_password2(self):
        """Valida se as senhas coincidem"""
        password1 = self.cleaned_data.get('new_password1')
        password2 = self.cleaned_data.get('new_password2')
        
        if password1 and password2:
            if password1 != password2:
                raise forms.ValidationError('As senhas não coincidem.')
        
        return password2
    
    def clean_new_password1(self):
        """Valida a nova senha"""
        password = self.cleaned_data.get('new_password1')
        
        if len(password) < 8:
            raise forms.ValidationError('A senha deve ter pelo menos 8 caracteres.')
        
        if password.isdigit():
            raise forms.ValidationError('A senha não pode conter apenas números.')
        
        if password.lower() == self.user.email.lower():
            raise forms.ValidationError('A senha não pode ser igual ao seu e-mail.')
        
        return password
