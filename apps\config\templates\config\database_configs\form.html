{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h3 mb-1">
                <i class="fas fa-database text-primary me-2"></i>
                {{ title }}
            </h2>
            <p class="text-muted mb-0">Configure conexão com banco de dados</p>
        </div>
        <div>
            <a href="{% url 'config:database_configs' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Voltar
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Configuração de Banco
                    </h5>
                </div>
                <div class="card-body">
                    {% crispy form %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Ajuda e Dicas -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Ajuda
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="helpAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#postgresqlHelp">
                                    <i class="fas fa-database me-2 text-info"></i>PostgreSQL
                                </button>
                            </h2>
                            <div id="postgresqlHelp" class="accordion-collapse collapse" 
                                 data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>Configurações típicas:</strong></p>
                                    <ul>
                                        <li>Engine: PostgreSQL</li>
                                        <li>Porta padrão: 5432</li>
                                        <li>Host: localhost (local) ou IP do servidor</li>
                                    </ul>
                                    <p><strong>Exemplo de opções JSON:</strong></p>
                                    <pre><code>{
  "sslmode": "require",
  "charset": "utf8"
}</code></pre>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#mysqlHelp">
                                    <i class="fas fa-database me-2 text-warning"></i>MySQL
                                </button>
                            </h2>
                            <div id="mysqlHelp" class="accordion-collapse collapse" 
                                 data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>Configurações típicas:</strong></p>
                                    <ul>
                                        <li>Engine: MySQL</li>
                                        <li>Porta padrão: 3306</li>
                                        <li>Host: localhost ou IP do servidor</li>
                                    </ul>
                                    <p><strong>Exemplo de opções JSON:</strong></p>
                                    <pre><code>{
  "charset": "utf8mb4",
  "init_command": "SET sql_mode='STRICT_TRANS_TABLES'"
}</code></pre>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#sqliteHelp">
                                    <i class="fas fa-file-alt me-2 text-secondary"></i>SQLite
                                </button>
                            </h2>
                            <div id="sqliteHelp" class="accordion-collapse collapse" 
                                 data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>Configurações SQLite:</strong></p>
                                    <ul>
                                        <li>Engine: SQLite</li>
                                        <li>Nome: Caminho para o arquivo .db</li>
                                        <li>Usuário/Senha: Não necessários</li>
                                        <li>Host/Porta: Não necessários</li>
                                    </ul>
                                    <p><strong>Exemplo:</strong></p>
                                    <ul>
                                        <li>Nome: <code>db.sqlite3</code></li>
                                        <li>Nome: <code>/path/to/database.db</code></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Status da Configuração (se editando) -->
            {% if config %}
            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="{% if config.is_active %}text-success{% else %}text-secondary{% endif %}">
                                {% if config.is_active %}Ativo{% else %}Inativo{% endif %}
                            </h5>
                            <small class="text-muted">Status</small>
                        </div>
                        <div class="col-6">
                            <h5 class="{% if config.last_test_result.success %}text-success{% else %}text-danger{% endif %}">
                                {% if config.last_tested_at %}
                                    {% if config.last_test_result.success %}✓{% else %}✗{% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </h5>
                            <small class="text-muted">Último Teste</small>
                        </div>
                    </div>
                    
                    {% if config.last_tested_at %}
                    <hr>
                    <p class="mb-0">
                        <strong>Último teste:</strong><br>
                        <small>{{ config.last_tested_at|date:"d/m/Y H:i" }}</small>
                    </p>
                    {% if config.last_test_result.message %}
                    <p class="mb-0">
                        <strong>Resultado:</strong><br>
                        <small>{{ config.last_test_result.message }}</small>
                    </p>
                    {% endif %}
                    {% endif %}
                    
                    <div class="d-grid mt-3">
                        <a href="{% url 'config:database_config_test' config.pk %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-vial me-2"></i>Testar Configuração
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function fillPostgreSQLConfig() {
    document.getElementById('id_engine').value = 'django.db.backends.postgresql';
    document.getElementById('id_host').value = 'localhost';
    document.getElementById('id_port').value = '5432';
    
    // Focus no campo de nome do banco
    document.getElementById('id_name_db').focus();
}

function fillMySQLConfig() {
    document.getElementById('id_engine').value = 'django.db.backends.mysql';
    document.getElementById('id_host').value = 'localhost';
    document.getElementById('id_port').value = '3306';
    
    document.getElementById('id_name_db').focus();
}

function fillSQLiteConfig() {
    document.getElementById('id_engine').value = 'django.db.backends.sqlite3';
    document.getElementById('id_name_db').value = 'db.sqlite3';
    document.getElementById('id_host').value = '';
    document.getElementById('id_port').value = '';
    document.getElementById('id_user').value = '';
    document.getElementById('id_password').value = '';
    
    document.getElementById('id_name_db').focus();
}

// Validação em tempo real
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    const engineSelect = document.getElementById('id_engine');
    
    // Função para mostrar/ocultar campos baseado no engine
    function toggleFieldsBasedOnEngine() {
        const engine = engineSelect.value;
        const userField = document.getElementById('id_user').closest('.form-group');
        const passwordField = document.getElementById('id_password').closest('.form-group');
        const hostField = document.getElementById('id_host').closest('.form-group');
        const portField = document.getElementById('id_port').closest('.form-group');
        
        if (engine === 'django.db.backends.sqlite3') {
            // SQLite não precisa de user, password, host, port
            if (userField) userField.style.display = 'none';
            if (passwordField) passwordField.style.display = 'none';
            if (hostField) hostField.style.display = 'none';
            if (portField) portField.style.display = 'none';
        } else {
            // Outros bancos precisam desses campos
            if (userField) userField.style.display = 'block';
            if (passwordField) passwordField.style.display = 'block';
            if (hostField) hostField.style.display = 'block';
            if (portField) portField.style.display = 'block';
        }
    }
    
    // Aplicar no carregamento da página
    if (engineSelect) {
        toggleFieldsBasedOnEngine();
        engineSelect.addEventListener('change', toggleFieldsBasedOnEngine);
    }
    
    // Feedback visual no envio
    if (form && submitBtn) {
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Salvando...';
            submitBtn.disabled = true;
        });
    }
    
    // Validação do JSON de opções
    const optionsField = document.getElementById('id_options');
    if (optionsField) {
        optionsField.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                try {
                    JSON.parse(value);
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } catch (e) {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
    }
});
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    font-size: 0.85em;
}

.form-group {
    transition: all 0.3s ease;
}
</style>
{% endblock %}
