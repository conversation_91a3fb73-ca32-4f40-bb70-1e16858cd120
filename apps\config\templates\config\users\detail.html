{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}

{% block config_title %}Detalhes do Usuário{% endblock %}

{% block breadcrumbs %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'config:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'config:user_list' %}">Usuários</a></li>
            <li class="breadcrumb-item active">{{ user_detail.email }}</li>
        </ol>
    </nav>
</div>
{% endblock %}

{% block config_content %}
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2">
            <i class="fas fa-user me-2"></i>Detalhes do Usuário
        </h1>
    </div>
    <div>
        <a href="{% url 'config:user_update' user_detail.slug %}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>Editar
        </a>
        <a href="{% url 'config:user_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Voltar
        </a>
    </div>
</div>

    <div class="row">
        <!-- Informações Básicas -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informações Básicas
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">E-mail:</label>
                            <p class="form-control-plaintext">{{ user_detail.email }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Nome de usuário:</label>
                            <p class="form-control-plaintext">{{ user_detail.username }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Nome:</label>
                            <p class="form-control-plaintext">{{ user_detail.first_name|default:"Não informado" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Sobrenome:</label>
                            <p class="form-control-plaintext">{{ user_detail.last_name|default:"Não informado" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Data de criação:</label>
                            <p class="form-control-plaintext">{{ user_detail.date_joined|date:"d/m/Y H:i" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Último acesso:</label>
                            <p class="form-control-plaintext">{{ user_detail.last_login|date:"d/m/Y H:i"|default:"Nunca" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status e Permissões -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Status e Permissões
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Status:</label>
                            <div>
                                {% if user_detail.is_active %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Ativo
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>Inativo
                                    </span>
                                {% endif %}
                                
                                {% if user_detail.is_verified %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-check-circle me-1"></i>Verificado
                                    </span>
                                {% else %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-exclamation-circle me-1"></i>Não verificado
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Tipo de usuário:</label>
                            <div>
                                {% if user_detail.is_superuser %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-crown me-1"></i>Superusuário
                                    </span>
                                {% elif user_detail.is_staff %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-user-tie me-1"></i>Staff
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-user me-1"></i>Usuário comum
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Slug:</label>
                            <p class="form-control-plaintext">{{ user_detail.slug|default:"Não gerado" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grupos -->
            {% if groups %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>Grupos
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for group in groups %}
                            <span class="badge bg-primary me-2 mb-2">
                                <i class="fas fa-users me-1"></i>{{ group.name }}
                            </span>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Permissões Específicas -->
            {% if permissions %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-key me-2"></i>Permissões Específicas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for permission in permissions %}
                                <div class="col-md-6 mb-2">
                                    <span class="badge bg-info">
                                        {{ permission.name }}
                                    </span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Ações -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Ações
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'config:user_update' user_detail.slug %}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>Editar Usuário
                        </a>

                        {% if user_detail != request.user %}
                            <a href="{% url 'config:user_delete' user_detail.slug %}" class="btn btn-outline-danger">
                                <i class="fas fa-trash me-1"></i>Deletar Usuário
                            </a>
                        {% endif %}
                        
                        <hr>
                        
                        <a href="{% url 'config:user_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-1"></i>Lista de Usuários
                        </a>
                        
                        <a href="{% url 'config:user_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-1"></i>Criar Novo Usuário
                        </a>
                    </div>
                </div>
            </div>

            <!-- Estatísticas -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Estatísticas
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-2">
                                <h6 class="text-muted mb-1">Grupos</h6>
                                <h4 class="text-primary">{{ groups.count }}</h4>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-2">
                                <h6 class="text-muted mb-1">Permissões</h6>
                                <h4 class="text-info">{{ permissions.count }}</h4>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Usuário criado há {{ user_detail.date_joined|timesince }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
