{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}

{% block config_title %}Lista de Configurações{% endblock %}

{% block breadcrumbs %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'config:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'config:system_config' %}">Sistema</a></li>
            <li class="breadcrumb-item active">Configurações</li>
        </ol>
    </nav>
</div>
{% endblock %}

{% block config_content %}
<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-list me-2 text-primary"></i>Configurações do Sistema
                </h1>
                <p class="text-muted mb-0">Gerencie todas as configurações do sistema</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'config:system_config_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Nova Configuração
                </a>
                <a href="{% url 'config:system_config' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-tachometer-alt me-1"></i>Visão Geral
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Filtros e Busca
                    </h6>
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    {% crispy form %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Configurações -->
<div class="row">
    <div class="col-12">
        {% if configs %}
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 ps-4">Chave</th>
                                    <th class="border-0">Valor</th>
                                    <th class="border-0">Descrição</th>
                                    <th class="border-0">Status</th>
                                    <th class="border-0">Atualizado</th>
                                    <th class="border-0 text-center">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for config in configs %}
                                <tr class="config-row" data-config-key="{{ config.key }}">
                                    <td class="ps-4">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 35px; height: 35px;">
                                                    <i class="fas fa-cog"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div class="fw-semibold">{{ config.key }}</div>
                                                <div class="text-muted small">
                                                    {% if config.key|length > 20 %}
                                                        {{ config.key|slice:":20" }}...
                                                    {% else %}
                                                        {{ config.key }}
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="value-preview">
                                            {% if config.value|length > 50 %}
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ config.value }}">
                                                    {{ config.value|slice:":50" }}...
                                                </span>
                                            {% else %}
                                                <span>{{ config.value }}</span>
                                            {% endif %}
                                        </div>
                                        <div class="small text-muted">
                                            {% if config.value|first == '{' or config.value|first == '[' %}
                                                <i class="fas fa-code me-1"></i>JSON
                                            {% else %}
                                                <i class="fas fa-font me-1"></i>Texto
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="description-preview">
                                            {% if config.description %}
                                                {% if config.description|length > 60 %}
                                                    <span class="text-truncate d-inline-block" style="max-width: 250px;" title="{{ config.description }}">
                                                        {{ config.description|slice:":60" }}...
                                                    </span>
                                                {% else %}
                                                    {{ config.description }}
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">Sem descrição</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if config.is_active %}
                                            <span class="badge bg-success-subtle text-success">
                                                <i class="fas fa-check-circle me-1"></i>Ativo
                                            </span>
                                        {% else %}
                                            <span class="badge bg-danger-subtle text-danger">
                                                <i class="fas fa-times-circle me-1"></i>Inativo
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="text-muted small">
                                            {% if config.updated_at %}
                                                <i class="fas fa-clock me-1"></i>{{ config.updated_at|timesince }} atrás
                                                <div class="text-muted smaller">{{ config.updated_at|date:"d/m/Y H:i" }}</div>
                                            {% else %}
                                                <i class="fas fa-minus me-1"></i>Nunca
                                            {% endif %}
                                        </div>
                                        {% if config.updated_by %}
                                            <div class="text-muted smaller">
                                                por {{ config.updated_by.get_full_name|default:config.updated_by.username }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#viewModal"
                                                    data-config-key="{{ config.key }}"
                                                    data-config-value="{{ config.value }}"
                                                    data-config-description="{{ config.description }}"
                                                    title="Visualizar">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="{% url 'config:system_config_update' config.key %}" 
                                               class="btn btn-outline-secondary" 
                                               title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'config:system_config_delete' config.key %}" 
                                               class="btn btn-outline-danger" 
                                               title="Deletar">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Paginação -->
            {% if page_obj.has_other_pages %}
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="text-muted">
                    Mostrando {{ page_obj.start_index }} a {{ page_obj.end_index }} de {{ page_obj.paginator.count }} configurações
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <!-- Estado vazio -->
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-cogs fa-4x text-muted opacity-50"></i>
                </div>
                <h4 class="text-muted mb-3">Nenhuma configuração encontrada</h4>
                {% if request.GET.query %}
                    <p class="text-muted mb-4">
                        Não encontramos configurações para "{{ request.GET.query }}". 
                        Tente ajustar os filtros ou criar uma nova configuração.
                    </p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="{% url 'config:system_config_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>Limpar Filtros
                        </a>
                        <a href="{% url 'config:system_config_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Criar Configuração
                        </a>
                    </div>
                {% else %}
                    <p class="text-muted mb-4">
                        Ainda não há configurações cadastradas no sistema. 
                        Comece criando a primeira configuração.
                    </p>
                    <a href="{% url 'config:system_config_create' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>Criar Primeira Configuração
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal de Visualização -->
<div class="modal fade" id="viewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>Visualizar Configuração
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label fw-bold">Chave:</label>
                        <p id="modalKey" class="form-control-plaintext"></p>
                    </div>
                    <div class="col-md-8">
                        <label class="form-label fw-bold">Descrição:</label>
                        <p id="modalDescription" class="form-control-plaintext"></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <label class="form-label fw-bold">Valor:</label>
                        <pre id="modalValue" class="bg-light p-3 rounded"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-primary" id="editFromModal">
                    <i class="fas fa-edit me-1"></i>Editar
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.config-row {
    transition: background-color 0.2s ease;
}

.config-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.smaller {
    font-size: 0.7rem;
}

.value-preview, .description-preview {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal de visualização
    const viewModal = document.getElementById('viewModal');
    if (viewModal) {
        viewModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const key = button.getAttribute('data-config-key');
            const value = button.getAttribute('data-config-value');
            const description = button.getAttribute('data-config-description');
            
            document.getElementById('modalKey').textContent = key;
            document.getElementById('modalDescription').textContent = description || 'Sem descrição';
            document.getElementById('modalValue').textContent = value;
            
            // Botão de editar
            const editBtn = document.getElementById('editFromModal');
            editBtn.onclick = function() {
                window.location.href = `/config/sistema/configuracoes/${key}/editar/`;
            };
        });
    }
    
    // Tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
