from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from accounts.interfaces.notifications import INotificationService

class EmailNotificationService(INotificationService):
    """Serviço de notificação por e-mail"""
    
    def send_registration_confirmation(self, email: str, code: str) -> None:
        """Envia e-mail de confirmação de registro"""
        subject = 'Confirmação de Cadastro'
        html_message = render_to_string(
            'accounts/emails/registration_confirmation.html',
            {'email': email, 'code': code}
        )
        plain_message = strip_tags(html_message)
        
        send_mail(
            subject,
            plain_message,
            settings.DEFAULT_FROM_EMAIL,
            [email],
            html_message=html_message,
            fail_silently=False,
        )
    
    def send_password_reset_code(self, email: str, code: str) -> None:
        """Envia e-mail com código para reset de senha"""
        subject = 'Código para Redefinição de Senha'
        html_message = render_to_string(
            'accounts/emails/password_reset.html',
            {'email': email, 'code': code}
        )
        plain_message = strip_tags(html_message)
        
        send_mail(
            subject,
            plain_message,
            settings.DEFAULT_FROM_EMAIL,
            [email],
            html_message=html_message,
            fail_silently=False,
        )
    
    def send_email_change_confirmation(self, email: str, code: str) -> None:
        """Envia e-mail de confirmação de alteração de e-mail"""
        subject = 'Confirmação de Alteração de E-mail'
        html_message = render_to_string(
            'accounts/emails/email_change.html',
            {'email': email, 'code': code}
        )
        plain_message = strip_tags(html_message)
        
        send_mail(
            subject,
            plain_message,
            settings.DEFAULT_FROM_EMAIL,
            [email],
            html_message=html_message,
            fail_silently=False,
        )