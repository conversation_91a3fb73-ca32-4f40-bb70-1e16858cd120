from django.views import View
from django.shortcuts import render, redirect
from django.contrib.auth import login, logout
from django.contrib import messages

from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
from apps.accounts.services.auth_service import AuthService
from apps.accounts.repositories.user_repository import DjangoUserRepository

@method_decorator([csrf_protect, never_cache], name='dispatch')
class LoginView(View):
    """View para login de usuários"""
    template_name = 'accounts/login.html'

    def get(self, request):
        """Exibe o formulário de login"""
        if request.user.is_authenticated:
            messages.info(request, 'Você já está logado.')
            return redirect('pages:home')
        return render(request, self.template_name)

    def post(self, request):
        """Processa o login"""
        email = request.POST.get('email', '').strip()
        password = request.POST.get('password', '')

        # Validações básicas
        if not email:
            messages.error(request, '📧 Por favor, digite seu e-mail.')
            return render(request, self.template_name)

        if not password:
            messages.error(request, '🔒 Por favor, digite sua senha.')
            return render(request, self.template_name)

        service = AuthService(user_repository=DjangoUserRepository())

        try:
            user = service.authenticate_user(email, password)
            if user:
                login(request, user)

                # Mensagem personalizada baseada no horário
                from datetime import datetime
                hour = datetime.now().hour
                if 5 <= hour < 12:
                    greeting = "Bom dia"
                elif 12 <= hour < 18:
                    greeting = "Boa tarde"
                else:
                    greeting = "Boa noite"

                name = user.get_full_name() or user.first_name or user.email.split('@')[0]
                messages.success(request, f'🎉 {greeting}, {name}! Login realizado com sucesso.')

                # Redireciona para a página solicitada ou home
                next_url = request.GET.get('next', 'pages:home')
                return redirect(next_url)
            else:
                messages.error(request, '❌ E-mail ou senha incorretos. Verifique seus dados e tente novamente.')
        except ValueError as e:
            if 'não verificado' in str(e).lower():
                messages.warning(request, '⚠️ Sua conta ainda não foi verificada. Verifique seu e-mail.')
            else:
                messages.error(request, f'❌ {str(e)}')
        except Exception as e:
            messages.error(request, '🔧 Ocorreu um erro durante o login. Tente novamente em alguns instantes.')

        return render(request, self.template_name)

class LogoutView(View):
    """View para logout de usuários"""

    def get(self, request):
        """Processa o logout"""
        if request.user.is_authenticated:
            logout(request)
            messages.success(request, 'Você foi desconectado com sucesso.')
        return redirect('pages:home')

    def post(self, request):
        """Processa o logout via POST"""
        return self.get(request)