from django.views import View
from django.shortcuts import render, redirect
from django.contrib.auth import login, logout
from django.contrib import messages
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
from apps.accounts.services.auth_service import AuthService
from apps.accounts.repositories.user_repository import DjangoUserRepository

@method_decorator([csrf_protect, never_cache], name='dispatch')
class LoginView(View):
    """View para login de usuários"""
    template_name = 'accounts/login.html'

    def get(self, request):
        """Exibe o formulário de login"""
        if request.user.is_authenticated:
            messages.info(request, 'Você já está logado.')
            return redirect('pages:home')
        return render(request, self.template_name)

    def post(self, request):
        """Processa o login"""
        email = request.POST.get('email')
        password = request.POST.get('password')

        if not email or not password:
            messages.error(request, 'Email e senha são obrigatórios.')
            return render(request, self.template_name)

        service = AuthService(user_repository=DjangoUserRepository())

        try:
            user = service.authenticate_user(email, password)
            if user:
                login(request, user)
                messages.success(request, f'Bem-vindo, {user.get_full_name() or user.email}!')

                # Redireciona para a página solicitada ou home
                next_url = request.GET.get('next', 'pages:home')
                return redirect(next_url)
            else:
                messages.error(request, 'Email ou senha incorretos.')
        except ValueError as e:
            messages.error(request, str(e))
        except Exception as e:
            messages.error(request, 'Ocorreu um erro durante o login. Tente novamente.')

        return render(request, self.template_name)

class LogoutView(View):
    """View para logout de usuários"""

    def get(self, request):
        """Processa o logout"""
        if request.user.is_authenticated:
            logout(request)
            messages.success(request, 'Você foi desconectado com sucesso.')
        return redirect('/')

    def post(self, request):
        """Processa o logout via POST"""
        return self.get(request)