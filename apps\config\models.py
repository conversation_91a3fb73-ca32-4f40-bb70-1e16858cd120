from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinLengthValidator
import json

User = get_user_model()

class SystemConfiguration(models.Model):
    """Modelo para configurações do sistema"""

    key = models.CharField(
        'chave',
        max_length=100,
        unique=True,
        validators=[MinLengthValidator(3)],
        help_text='Chave única da configuração'
    )
    value = models.TextField(
        'valor',
        help_text='Valor da configuração (pode ser JSON)'
    )
    description = models.TextField(
        'descrição',
        blank=True,
        help_text='Descrição da configuração'
    )
    is_active = models.BooleanField(
        'ativo',
        default=True,
        help_text='Se a configuração está ativa'
    )
    created_at = models.DateTimeField(
        'criado em',
        auto_now_add=True
    )
    updated_at = models.DateTimeField(
        'atualizado em',
        auto_now=True
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='atualizado por',
        help_text='Usuário que fez a última atualização'
    )

    class Meta:
        verbose_name = 'configuração do sistema'
        verbose_name_plural = 'configurações do sistema'
        ordering = ['key']

    def __str__(self):
        return f"{self.key}: {self.value[:50]}..."

    def get_value_as_json(self):
        """Retorna o valor como JSON se possível"""
        try:
            return json.loads(self.value)
        except (json.JSONDecodeError, TypeError):
            return self.value

    def set_value_from_dict(self, data):
        """Define o valor a partir de um dicionário"""
        self.value = json.dumps(data, ensure_ascii=False, indent=2)


class UserActivityLog(models.Model):
    """Modelo para logs de atividade dos usuários"""

    ACTION_CHOICES = [
        ('CREATE', 'Criação'),
        ('UPDATE', 'Atualização'),
        ('DELETE', 'Exclusão'),
        ('LOGIN', 'Login'),
        ('LOGOUT', 'Logout'),
        ('PASSWORD_CHANGE', 'Alteração de Senha'),
        ('PERMISSION_CHANGE', 'Alteração de Permissão'),
        ('GROUP_CHANGE', 'Alteração de Grupo'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='usuário',
        related_name='activity_logs'
    )
    action = models.CharField(
        'ação',
        max_length=20,
        choices=ACTION_CHOICES
    )
    target_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='usuário alvo',
        related_name='target_logs',
        help_text='Usuário que foi afetado pela ação (se aplicável)'
    )
    description = models.TextField(
        'descrição',
        help_text='Descrição detalhada da ação'
    )
    ip_address = models.GenericIPAddressField(
        'endereço IP',
        null=True,
        blank=True
    )
    user_agent = models.TextField(
        'user agent',
        blank=True,
        help_text='Informações do navegador/cliente'
    )
    extra_data = models.JSONField(
        'dados extras',
        default=dict,
        blank=True,
        help_text='Dados adicionais em formato JSON'
    )
    created_at = models.DateTimeField(
        'criado em',
        auto_now_add=True
    )

    class Meta:
        verbose_name = 'log de atividade'
        verbose_name_plural = 'logs de atividade'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['action', '-created_at']),
            models.Index(fields=['target_user', '-created_at']),
        ]

    def __str__(self):
        target = f" -> {self.target_user.email}" if self.target_user else ""
        return f"{self.user.email}: {self.get_action_display()}{target} ({self.created_at})"
