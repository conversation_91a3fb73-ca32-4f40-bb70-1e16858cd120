from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.auth import get_user_model
from django.views import View
from django.core.paginator import Paginator
from django.db.models import Q
from apps.config.forms.user_forms import UserCreateForm, UserUpdateForm, UserSearchForm
from apps.config.services.user_management_service import UserManagementService
from apps.config.services.system_config_service import AuditLogService
from apps.config.repositories.user_repository import DjangoUserRepository
from apps.config.repositories.config_repository import DjangoAuditLogRepository

User = get_user_model()

class UserListView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para listar usuários"""
    permission_required = 'auth.view_user'
    template_name = 'config/users/list.html'

    def get(self, request):
        """Lista usuários com filtros e busca"""
        form = UserSearchForm(request.GET)
        users = User.objects.all()

        # Aplica filtros
        if form.is_valid():
            query = form.cleaned_data.get('query')
            is_active = form.cleaned_data.get('is_active')
            is_staff = form.cleaned_data.get('is_staff')
            is_superuser = form.cleaned_data.get('is_superuser')

            if query:
                users = users.filter(
                    Q(email__icontains=query) |
                    Q(username__icontains=query) |
                    Q(first_name__icontains=query) |
                    Q(last_name__icontains=query)
                )

            if is_active:
                users = users.filter(is_active=is_active == 'true')

            if is_staff:
                users = users.filter(is_staff=is_staff == 'true')

            if is_superuser:
                users = users.filter(is_superuser=is_superuser == 'true')

        # Paginação
        paginator = Paginator(users.order_by('-date_joined'), 25)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'form': form,
            'page_obj': page_obj,
            'users': page_obj.object_list,
        }

        return render(request, self.template_name, context)


class UserCreateView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para criar usuários"""
    permission_required = 'auth.add_user'
    template_name = 'config/users/create.html'

    def get(self, request):
        """Exibe formulário de criação"""
        form = UserCreateForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        """Processa criação do usuário"""
        form = UserCreateForm(request.POST)

        if form.is_valid():
            try:
                # Prepara dados do usuário
                user_data = {
                    'email': form.cleaned_data['email'],
                    'username': form.cleaned_data['username'],
                    'first_name': form.cleaned_data['first_name'],
                    'last_name': form.cleaned_data['last_name'],
                    'password': form.cleaned_data['password1'],
                    'is_active': form.cleaned_data['is_active'],
                    'is_staff': form.cleaned_data['is_staff'],
                    'is_superuser': form.cleaned_data['is_superuser'],
                }

                # Cria o usuário usando o service
                audit_service = AuditLogService(DjangoAuditLogRepository())
                user_service = UserManagementService(DjangoUserRepository(), audit_service)

                user = user_service.create_user(user_data, request.user)

                # Atribui grupos
                groups = form.cleaned_data.get('groups', [])
                if groups:
                    user.groups.set(groups)

                messages.success(request, f'Usuário {user.email} criado com sucesso!')
                return redirect('config:user_list')

            except ValueError as e:
                form.add_error(None, str(e))
            except Exception as e:
                form.add_error(None, 'Erro interno. Tente novamente.')

        return render(request, self.template_name, {'form': form})


class UserDetailView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para visualizar detalhes do usuário"""
    permission_required = 'auth.view_user'
    template_name = 'config/users/detail.html'

    def get(self, request, user_id):
        """Exibe detalhes do usuário"""
        try:
            user = User.objects.get(id=user_id)
            context = {
                'user_detail': user,
                'groups': user.groups.all(),
                'permissions': user.user_permissions.all(),
            }
            return render(request, self.template_name, context)
        except User.DoesNotExist:
            messages.error(request, 'Usuário não encontrado.')
            return redirect('config:user_list')


class UserUpdateView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para editar usuários"""
    permission_required = 'auth.change_user'
    template_name = 'config/users/update.html'

    def get(self, request, user_id):
        """Exibe formulário de edição"""
        try:
            user = User.objects.get(id=user_id)
            form = UserUpdateForm(instance=user)
            return render(request, self.template_name, {'form': form, 'user_detail': user})
        except User.DoesNotExist:
            messages.error(request, 'Usuário não encontrado.')
            return redirect('config:user_list')

    def post(self, request, user_id):
        """Processa edição do usuário"""
        try:
            user = User.objects.get(id=user_id)
            form = UserUpdateForm(request.POST, instance=user)

            if form.is_valid():
                try:
                    # Atualiza dados básicos
                    updated_user = form.save()

                    # Atualiza grupos
                    groups = form.cleaned_data.get('groups', [])
                    updated_user.groups.set(groups)

                    # Log da ação
                    audit_service = AuditLogService(DjangoAuditLogRepository())
                    audit_service.log_action(
                        user=request.user,
                        action='UPDATE_USER',
                        details=f'Usuário {updated_user.email} atualizado'
                    )

                    messages.success(request, f'Usuário {updated_user.email} atualizado com sucesso!')
                    return redirect('config:user_detail', user_id=updated_user.id)

                except Exception as e:
                    form.add_error(None, f'Erro ao atualizar usuário: {str(e)}')

            return render(request, self.template_name, {'form': form, 'user_detail': user})

        except User.DoesNotExist:
            messages.error(request, 'Usuário não encontrado.')
            return redirect('config:user_list')


class UserDeleteView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para deletar usuários"""
    permission_required = 'auth.delete_user'
    template_name = 'config/users/delete.html'

    def get(self, request, user_id):
        """Exibe confirmação de exclusão"""
        try:
            user = User.objects.get(id=user_id)

            # Não permite deletar o próprio usuário
            if user == request.user:
                messages.error(request, 'Você não pode deletar sua própria conta.')
                return redirect('config:user_list')

            # Não permite deletar superusuários (exceto por outros superusuários)
            if user.is_superuser and not request.user.is_superuser:
                messages.error(request, 'Você não tem permissão para deletar superusuários.')
                return redirect('config:user_list')

            return render(request, self.template_name, {'user_detail': user})
        except User.DoesNotExist:
            messages.error(request, 'Usuário não encontrado.')
            return redirect('config:user_list')

    def post(self, request, user_id):
        """Processa exclusão do usuário"""
        try:
            user = User.objects.get(id=user_id)

            # Validações de segurança
            if user == request.user:
                messages.error(request, 'Você não pode deletar sua própria conta.')
                return redirect('config:user_list')

            if user.is_superuser and not request.user.is_superuser:
                messages.error(request, 'Você não tem permissão para deletar superusuários.')
                return redirect('config:user_list')

            # Salva informações para o log antes de deletar
            user_email = user.email

            # Deleta o usuário
            user.delete()

            # Log da ação
            audit_service = AuditLogService(DjangoAuditLogRepository())
            audit_service.log_action(
                user=request.user,
                action='DELETE_USER',
                details=f'Usuário {user_email} deletado'
            )

            messages.success(request, f'Usuário {user_email} deletado com sucesso!')
            return redirect('config:user_list')

        except User.DoesNotExist:
            messages.error(request, 'Usuário não encontrado.')
            return redirect('config:user_list')
