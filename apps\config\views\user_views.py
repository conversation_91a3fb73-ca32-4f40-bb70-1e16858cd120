from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.auth import get_user_model
from django.views import View
from django.core.paginator import Paginator
from django.db.models import Q
from apps.config.forms.user_forms import UserCreateForm, UserSearchForm
from apps.config.services.user_management_service import UserManagementService
from apps.config.services.system_config_service import AuditLogService
from apps.config.repositories.user_repository import DjangoUserRepository
from apps.config.repositories.config_repository import DjangoAuditLogRepository

User = get_user_model()

class UserListView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para listar usuários"""
    permission_required = 'auth.view_user'
    template_name = 'config/users/list.html'

    def get(self, request):
        """Lista usuários com filtros e busca"""
        form = UserSearchForm(request.GET)
        users = User.objects.all()

        # Aplica filtros
        if form.is_valid():
            query = form.cleaned_data.get('query')
            is_active = form.cleaned_data.get('is_active')
            is_staff = form.cleaned_data.get('is_staff')
            is_superuser = form.cleaned_data.get('is_superuser')

            if query:
                users = users.filter(
                    Q(email__icontains=query) |
                    Q(username__icontains=query) |
                    Q(first_name__icontains=query) |
                    Q(last_name__icontains=query)
                )

            if is_active:
                users = users.filter(is_active=is_active == 'true')

            if is_staff:
                users = users.filter(is_staff=is_staff == 'true')

            if is_superuser:
                users = users.filter(is_superuser=is_superuser == 'true')

        # Paginação
        paginator = Paginator(users.order_by('-date_joined'), 25)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'form': form,
            'page_obj': page_obj,
            'users': page_obj.object_list,
        }

        return render(request, self.template_name, context)


class UserCreateView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para criar usuários"""
    permission_required = 'auth.add_user'
    template_name = 'config/users/create.html'

    def get(self, request):
        """Exibe formulário de criação"""
        form = UserCreateForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        """Processa criação do usuário"""
        form = UserCreateForm(request.POST)

        if form.is_valid():
            try:
                # Prepara dados do usuário
                user_data = {
                    'email': form.cleaned_data['email'],
                    'username': form.cleaned_data['username'],
                    'first_name': form.cleaned_data['first_name'],
                    'last_name': form.cleaned_data['last_name'],
                    'password': form.cleaned_data['password1'],
                    'is_active': form.cleaned_data['is_active'],
                    'is_staff': form.cleaned_data['is_staff'],
                    'is_superuser': form.cleaned_data['is_superuser'],
                }

                # Cria o usuário usando o service
                audit_service = AuditLogService(DjangoAuditLogRepository())
                user_service = UserManagementService(DjangoUserRepository(), audit_service)

                user = user_service.create_user(user_data, request.user)

                # Atribui grupos
                groups = form.cleaned_data.get('groups', [])
                if groups:
                    user.groups.set(groups)

                messages.success(request, f'Usuário {user.email} criado com sucesso!')
                return redirect('config:user_list')

            except ValueError as e:
                form.add_error(None, str(e))
            except Exception as e:
                form.add_error(None, 'Erro interno. Tente novamente.')

        return render(request, self.template_name, {'form': form})
