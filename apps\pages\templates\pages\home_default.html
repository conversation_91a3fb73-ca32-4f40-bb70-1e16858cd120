{% extends 'pages/base.html' %}

{% block content %}
<div class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold">Bem-vindo ao Havoc</h1>
                <p class="lead">Sistema moderno de gerenciamento de conteúdo com arquitetura limpa e princípios SOLID.</p>
                <div class="mt-4">
                    <a href="{% url 'pages:about' %}" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-info-circle me-2"></i>Saiba Mais
                    </a>
                    <a href="{% url 'pages:contact' %}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>Contato
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-rocket fa-10x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2>Sistema em Funcionamento</h2>
            <p class="text-muted">O app Pages está funcionando corretamente!</p>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-check-circle fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">App Pages</h5>
                    <p class="card-text">Sistema de páginas implementado com arquitetura limpa e princípios SOLID.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-users fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">App Accounts</h5>
                    <p class="card-text">Sistema completo de autenticação e gerenciamento de usuários.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-cog fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">App Config</h5>
                    <p class="card-text">Painel administrativo para configuração e gerenciamento do sistema.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-light py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h3>Ações Rápidas</h3>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    {% if user.is_authenticated %}
                        {% if user.is_staff %}
                            <a href="{% url 'config:dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard Admin
                            </a>
                            <a href="/admin/" class="btn btn-secondary">
                                <i class="fas fa-cog me-2"></i>Django Admin
                            </a>
                        {% endif %}
                        <a href="{% url 'accounts:logout' %}" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>Sair
                        </a>
                    {% else %}
                        <a href="{% url 'accounts:login' %}" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Fazer Login
                        </a>
                        <a href="{% url 'accounts:register' %}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>Registrar-se
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
