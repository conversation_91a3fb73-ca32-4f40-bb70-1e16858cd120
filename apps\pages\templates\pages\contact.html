{% extends 'pages/base.html' %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="text-center mb-5">
                <h1 class="display-4">Entre em Contato</h1>
                <p class="lead text-muted">Estamos aqui para ajudar você</p>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-envelope me-2"></i>Envie uma Mensagem</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="mb-3">
                                    {{ form.name.label_tag }}
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.email.label_tag }}
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.subject.label_tag }}
                                    {{ form.subject }}
                                    {% if form.subject.errors %}
                                        <div class="text-danger small">{{ form.subject.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.message.label_tag }}
                                    {{ form.message }}
                                    {% if form.message.errors %}
                                        <div class="text-danger small">{{ form.message.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Enviar Mensagem
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-0 bg-light">
                        <div class="card-body">
                            <h6><i class="fas fa-info-circle text-primary me-2"></i>Informações</h6>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block">Email</small>
                                <strong><EMAIL></strong>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block">Telefone</small>
                                <strong>(11) 9999-9999</strong>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block">Horário de Atendimento</small>
                                <strong>Segunda a Sexta<br>9h às 18h</strong>
                            </div>
                            
                            <hr>
                            
                            <h6><i class="fas fa-share-alt text-primary me-2"></i>Redes Sociais</h6>
                            <div class="d-flex gap-2">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-facebook"></i>
                                </a>
                                <a href="#" class="btn btn-outline-info btn-sm">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="btn btn-outline-danger btn-sm">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card border-0 bg-light mt-3">
                        <div class="card-body">
                            <h6><i class="fas fa-question-circle text-primary me-2"></i>Precisa de Ajuda?</h6>
                            <p class="small text-muted">
                                Consulte nossa documentação ou entre em contato conosco para suporte técnico.
                            </p>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-book me-1"></i>Documentação
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
