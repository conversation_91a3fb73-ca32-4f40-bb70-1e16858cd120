<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Configurações</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'config:dashboard' %}">Config Admin</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{% url 'config:user_list' %}">Usuários</a>
                <a class="nav-link" href="/admin/">Django Admin</a>
                <a class="nav-link" href="{% url 'accounts:logout' %}">Sair</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1>Dashboard de Configurações</h1>
                <p class="text-muted">Sistema de gerenciamento de usuários e permissões</p>
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">Total de Usuários</h5>
                        <h2>{{ total_users }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">Usuários Ativos</h5>
                        <h2>{{ active_users }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h5 class="card-title">Staff</h5>
                        <h2>{{ staff_users }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h5 class="card-title">Grupos</h5>
                        <h2>{{ total_groups }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>Ações Rápidas</h3>
                <div class="d-flex gap-2">
                    <a href="{% url 'config:user_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Criar Usuário
                    </a>
                    <a href="{% url 'config:user_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-users"></i> Gerenciar Usuários
                    </a>
                    <a href="/admin/" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> Django Admin
                    </a>
                </div>
            </div>
        </div>

        <!-- Atividades Recentes -->
        <div class="row">
            <div class="col-12">
                <h3>Atividades Recentes</h3>
                <div class="card">
                    <div class="card-body">
                        {% if recent_activities %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Usuário</th>
                                            <th>Ação</th>
                                            <th>Descrição</th>
                                            <th>Data</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for activity in recent_activities %}
                                        <tr>
                                            <td>{{ activity.user.email }}</td>
                                            <td>
                                                <span class="badge bg-secondary">{{ activity.get_action_display }}</span>
                                            </td>
                                            <td>{{ activity.description|truncatechars:50 }}</td>
                                            <td>{{ activity.created_at|date:"d/m/Y H:i" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">Nenhuma atividade recente encontrada.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
</body>
</html>
