{% extends 'config/base_config.html' %}

{% block config_title %}Dashboard{% endblock %}

{% block config_content %}
        <div class="row">
            <div class="col-12">
                <h1>Dashboard de Configurações</h1>
                <p class="text-muted">Sistema de gerenciamento de usuários e permissões</p>
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">Total de Usuários</h5>
                        <h2>{{ total_users }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">Usuários Ativos</h5>
                        <h2>{{ active_users }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h5 class="card-title">Staff</h5>
                        <h2>{{ staff_users }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h5 class="card-title">Grupos</h5>
                        <h2>{{ total_groups }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>Ações Rápidas</h3>
                <div class="d-flex gap-2">
                    <a href="{% url 'config:user_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Criar Usuário
                    </a>
                    <a href="{% url 'config:user_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-users"></i> Gerenciar Usuários
                    </a>
                </div>
            </div>
        </div>

        <!-- Atividades Recentes -->
        <div class="row">
            <div class="col-12">
                <h3>Atividades Recentes</h3>
                <div class="card">
                    <div class="card-body">
                        {% if recent_activities %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Usuário</th>
                                            <th>Ação</th>
                                            <th>Descrição</th>
                                            <th>Data</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for activity in recent_activities %}
                                        <tr>
                                            <td>{{ activity.user.email }}</td>
                                            <td>
                                                <span class="badge bg-secondary">{{ activity.get_action_display }}</span>
                                            </td>
                                            <td>{{ activity.description|truncatechars:50 }}</td>
                                            <td>{{ activity.created_at|date:"d/m/Y H:i" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">Nenhuma atividade recente encontrada.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
{% endblock %}
