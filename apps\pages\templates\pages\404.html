{% extends 'base.html' %}

{% block title %}Página não encontrada - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <!-- 404 Icon -->
            <div class="mb-5">
                <i class="fas fa-exclamation-triangle fa-5x text-warning mb-4"></i>
                <h1 class="display-1 fw-bold text-muted">404</h1>
                <h2 class="h3 mb-3">Página não encontrada</h2>
                <p class="lead text-muted">
                    {% if error %}
                        {{ error }}
                    {% else %}
                        A página que você está procurando não existe ou foi movida.
                    {% endif %}
                </p>
            </div>

            <!-- Search Box -->
            <div class="card mb-5">
                <div class="card-body">
                    <h5 class="card-title">Tente buscar pelo que você precisa:</h5>
                    <form method="get" action="{% url 'pages:search' %}" class="row g-3 justify-content-center">
                        <div class="col-md-8">
                            <input type="search" class="form-control" name="q" 
                                   placeholder="Digite sua busca..." value="{{ slug|default:'' }}">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>Buscar
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="row mb-5">
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-home fa-2x text-primary mb-3"></i>
                            <h5 class="card-title">Página Inicial</h5>
                            <p class="card-text">Volte para a página principal do site.</p>
                            <a href="{% url 'pages:home' %}" class="btn btn-primary">
                                <i class="fas fa-home me-1"></i>Ir para Home
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-file-alt fa-2x text-info mb-3"></i>
                            <h5 class="card-title">Todas as Páginas</h5>
                            <p class="card-text">Navegue por todas as páginas disponíveis.</p>
                            <a href="{% url 'pages:page_list' %}" class="btn btn-info">
                                <i class="fas fa-list me-1"></i>Ver Páginas
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-envelope fa-2x text-success mb-3"></i>
                            <h5 class="card-title">Contato</h5>
                            <p class="card-text">Entre em contato conosco para ajuda.</p>
                            <a href="{% url 'pages:contact' %}" class="btn btn-success">
                                <i class="fas fa-envelope me-1"></i>Fale Conosco
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Help -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Precisa de mais ajuda?</h5>
                    <p class="card-text text-muted">
                        Se você chegou aqui através de um link, pode ser que ele esteja quebrado ou desatualizado.
                    </p>
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item border-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Verifique se o endereço foi digitado corretamente
                                </div>
                                <div class="list-group-item border-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Use o menu de navegação para encontrar o que procura
                                </div>
                                <div class="list-group-item border-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Tente usar a busca com palavras-chave
                                </div>
                                <div class="list-group-item border-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Entre em contato conosco se o problema persistir
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Back Button -->
            <div class="mt-4">
                <button onclick="history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on search input
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput) {
        searchInput.focus();
    }
    
    // Add some animation
    const cards = document.querySelectorAll('.card');
    cards.forEach(function(card, index) {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(function() {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
