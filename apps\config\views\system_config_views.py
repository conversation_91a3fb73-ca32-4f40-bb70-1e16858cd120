from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views import View
from django.core.paginator import Paginator
from django.db.models import Q
from django.conf import settings
from django.core.cache import cache
from django.db import connection
from apps.config.models import SystemConfiguration
from apps.config.services.system_config_service import SystemConfigService, AuditLogService
from apps.config.repositories.config_repository import DjangoSystemConfigRepository, DjangoAuditLogRepository
from apps.config.forms.system_config_forms import SystemConfigForm, SystemConfigSearchForm
import os
import sys
import platform
import psutil
from datetime import datetime


class SystemConfigView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View principal para configurações do sistema"""
    permission_required = 'auth.change_user'  # Permissão de staff
    template_name = 'config/system_config.html'

    def get(self, request):
        """Exibe página de configurações do sistema"""
        context = self.get_context_data()
        return render(request, self.template_name, context)

    def get_context_data(self):
        """Prepara dados do contexto"""
        # Configurações do sistema (usando um modelo mock se não existir)
        try:
            configs = SystemConfiguration.objects.filter(is_active=True).order_by('key')
        except:
            configs = []

        # Informações do sistema
        system_info = self.get_system_info()

        # Informações do Django
        django_info = self.get_django_info()

        # Informações do banco de dados
        database_info = self.get_database_info()

        # Configurações por categoria
        config_categories = self.categorize_configs(configs)

        # Estatísticas
        stats = self.get_system_stats()

        return {
            'configs': configs,
            'config_categories': config_categories,
            'system_info': system_info,
            'django_info': django_info,
            'database_info': database_info,
            'stats': stats,
        }

    def get_system_info(self):
        """Obtém informações do sistema operacional"""
        try:
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'architecture': platform.architecture()[0],
                'processor': platform.processor() or 'N/A',
                'hostname': platform.node(),
                'cpu_count': psutil.cpu_count(),
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_total': self.format_bytes(memory.total),
                'memory_used': self.format_bytes(memory.used),
                'memory_percent': memory.percent,
                'disk_total': self.format_bytes(disk.total),
                'disk_used': self.format_bytes(disk.used),
                'disk_percent': (disk.used / disk.total) * 100,
                'uptime': datetime.now() - datetime.fromtimestamp(psutil.boot_time()),
            }
        except Exception as e:
            return {'error': str(e)}

    def get_django_info(self):
        """Obtém informações do Django"""
        return {
            'version': getattr(settings, 'DJANGO_VERSION', 'N/A'),
            'debug': settings.DEBUG,
            'secret_key_set': bool(getattr(settings, 'SECRET_KEY', None)),
            'allowed_hosts': settings.ALLOWED_HOSTS,
            'installed_apps_count': len(settings.INSTALLED_APPS),
            'middleware_count': len(settings.MIDDLEWARE),
            'time_zone': settings.TIME_ZONE,
            'language_code': settings.LANGUAGE_CODE,
            'use_i18n': settings.USE_I18N,
            'use_l10n': getattr(settings, 'USE_L10N', False),
            'use_tz': settings.USE_TZ,
            'static_url': settings.STATIC_URL,
            'media_url': getattr(settings, 'MEDIA_URL', 'N/A'),
        }

    def get_database_info(self):
        """Obtém informações do banco de dados"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT version()")
                db_version = cursor.fetchone()[0] if cursor.rowcount > 0 else 'N/A'
                
                # Estatísticas das tabelas
                cursor.execute("""
                    SELECT 
                        schemaname,
                        tablename,
                        n_tup_ins as inserts,
                        n_tup_upd as updates,
                        n_tup_del as deletes
                    FROM pg_stat_user_tables 
                    ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC 
                    LIMIT 10
                """)
                table_stats = cursor.fetchall()
                
            return {
                'engine': connection.vendor,
                'version': db_version,
                'name': connection.settings_dict['NAME'],
                'host': connection.settings_dict.get('HOST', 'localhost'),
                'port': connection.settings_dict.get('PORT', 'default'),
                'table_stats': table_stats,
            }
        except Exception as e:
            return {
                'engine': connection.vendor,
                'error': str(e)
            }

    def categorize_configs(self, configs):
        """Categoriza configurações por prefixo"""
        categories = {}
        
        for config in configs:
            # Extrai categoria do prefixo da chave
            parts = config.key.split('_', 1)
            category = parts[0].upper() if len(parts) > 1 else 'GERAL'
            
            if category not in categories:
                categories[category] = []
            
            categories[category].append(config)
        
        return categories

    def get_system_stats(self):
        """Obtém estatísticas do sistema"""
        from django.contrib.auth import get_user_model
        from django.contrib.auth.models import Group

        User = get_user_model()

        stats = {
            'total_users': User.objects.count(),
            'active_users': User.objects.filter(is_active=True).count(),
            'staff_users': User.objects.filter(is_staff=True).count(),
            'superusers': User.objects.filter(is_superuser=True).count(),
            'total_groups': Group.objects.count(),
        }

        # Configurações (pode não existir ainda)
        try:
            stats['total_configs'] = SystemConfiguration.objects.count()
            stats['active_configs'] = SystemConfiguration.objects.filter(is_active=True).count()
        except:
            stats['total_configs'] = 0
            stats['active_configs'] = 0

        # Logs de atividade (pode não existir ainda)
        try:
            from apps.config.models import UserActivityLog
            stats['total_logs'] = UserActivityLog.objects.count()
            stats['recent_logs'] = UserActivityLog.objects.filter(
                created_at__gte=datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            ).count()
        except:
            stats['total_logs'] = 0
            stats['recent_logs'] = 0

        return stats

    def format_bytes(self, bytes_value):
        """Formata bytes em unidades legíveis"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} PB"


class SystemConfigListView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para listar configurações do sistema"""
    permission_required = 'auth.change_user'
    template_name = 'config/system_config_list.html'

    def get(self, request):
        """Lista configurações com filtros"""
        form = SystemConfigSearchForm(request.GET)
        configs = SystemConfiguration.objects.all()

        if form.is_valid():
            query = form.cleaned_data.get('query')
            is_active = form.cleaned_data.get('is_active')
            
            if query:
                configs = configs.filter(
                    Q(key__icontains=query) |
                    Q(description__icontains=query) |
                    Q(value__icontains=query)
                )
            
            if is_active is not None:
                configs = configs.filter(is_active=is_active)

        configs = configs.order_by('key')
        
        # Paginação
        paginator = Paginator(configs, 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'form': form,
            'configs': page_obj,
            'page_obj': page_obj,
        }
        
        return render(request, self.template_name, context)


class SystemConfigCreateView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para criar configuração do sistema"""
    permission_required = 'auth.add_user'
    template_name = 'config/system_config_create.html'

    def get(self, request):
        """Exibe formulário de criação"""
        form = SystemConfigForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        """Processa criação da configuração"""
        form = SystemConfigForm(request.POST)
        
        if form.is_valid():
            try:
                config_service = SystemConfigService(
                    DjangoSystemConfigRepository(),
                    AuditLogService(DjangoAuditLogRepository())
                )
                
                success = config_service.set_config(
                    key=form.cleaned_data['key'],
                    value=form.cleaned_data['value'],
                    description=form.cleaned_data['description'],
                    updated_by=request.user
                )
                
                if success:
                    messages.success(request, f'Configuração {form.cleaned_data["key"]} criada com sucesso!')
                    return redirect('config:system_config_list')
                else:
                    messages.error(request, 'Erro ao criar configuração.')
                    
            except Exception as e:
                messages.error(request, f'Erro ao criar configuração: {str(e)}')

        return render(request, self.template_name, {'form': form})


class SystemConfigUpdateView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para editar configuração do sistema"""
    permission_required = 'auth.change_user'
    template_name = 'config/system_config_update.html'

    def get(self, request, config_key):
        """Exibe formulário de edição"""
        try:
            config = SystemConfiguration.objects.get(key=config_key)
            form = SystemConfigForm(instance=config)
            return render(request, self.template_name, {'form': form, 'config': config})
        except SystemConfiguration.DoesNotExist:
            messages.error(request, 'Configuração não encontrada.')
            return redirect('config:system_config_list')

    def post(self, request, config_key):
        """Processa edição da configuração"""
        try:
            config = SystemConfiguration.objects.get(key=config_key)
            form = SystemConfigForm(request.POST, instance=config)
            
            if form.is_valid():
                config_service = SystemConfigService(
                    DjangoSystemConfigRepository(),
                    AuditLogService(DjangoAuditLogRepository())
                )
                
                success = config_service.set_config(
                    key=form.cleaned_data['key'],
                    value=form.cleaned_data['value'],
                    description=form.cleaned_data['description'],
                    updated_by=request.user
                )
                
                if success:
                    messages.success(request, f'Configuração {config_key} atualizada com sucesso!')
                    return redirect('config:system_config_list')
                else:
                    messages.error(request, 'Erro ao atualizar configuração.')
                    
            return render(request, self.template_name, {'form': form, 'config': config})
            
        except SystemConfiguration.DoesNotExist:
            messages.error(request, 'Configuração não encontrada.')
            return redirect('config:system_config_list')
        except Exception as e:
            messages.error(request, f'Erro ao atualizar configuração: {str(e)}')
            return redirect('config:system_config_list')


class SystemConfigDeleteView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """View para deletar configuração do sistema"""
    permission_required = 'auth.delete_user'
    template_name = 'config/system_config_delete.html'

    def get(self, request, config_key):
        """Exibe confirmação de exclusão"""
        try:
            config = SystemConfiguration.objects.get(key=config_key)
            return render(request, self.template_name, {'config': config})
        except SystemConfiguration.DoesNotExist:
            messages.error(request, 'Configuração não encontrada.')
            return redirect('config:system_config_list')

    def post(self, request, config_key):
        """Processa exclusão da configuração"""
        try:
            config = SystemConfiguration.objects.get(key=config_key)
            
            # Log da ação antes de deletar
            audit_service = AuditLogService(DjangoAuditLogRepository())
            audit_service.log_action(
                user=request.user,
                action='DELETE_CONFIG',
                details=f'Configuração {config_key} deletada'
            )
            
            config.delete()
            messages.success(request, f'Configuração {config_key} deletada com sucesso!')
            
        except SystemConfiguration.DoesNotExist:
            messages.error(request, 'Configuração não encontrada.')
        except Exception as e:
            messages.error(request, f'Erro ao deletar configuração: {str(e)}')
            
        return redirect('config:system_config_list')
