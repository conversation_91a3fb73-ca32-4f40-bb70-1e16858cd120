<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lista de Usuários - Configurações</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'config:dashboard' %}">Config Admin</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{% url 'config:user_list' %}">Usuários</a>
                <a class="nav-link" href="/admin/">Django Admin</a>
                <a class="nav-link" href="{% url 'accounts:logout' %}">Sair</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Gerenciar Usuários</h1>
                    <a href="{% url 'config:user_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Criar Usuário
                    </a>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Filtros</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                {{ form.query.label_tag }}
                                {{ form.query }}
                            </div>
                            <div class="col-md-2">
                                {{ form.is_active.label_tag }}
                                {{ form.is_active }}
                            </div>
                            <div class="col-md-2">
                                {{ form.is_staff.label_tag }}
                                {{ form.is_staff }}
                            </div>
                            <div class="col-md-2">
                                {{ form.is_superuser.label_tag }}
                                {{ form.is_superuser }}
                            </div>
                            <div class="col-md-2">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">Filtrar</button>
                                    <a href="{% url 'config:user_list' %}" class="btn btn-outline-secondary">Limpar</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Usuários -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        {% if users %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Email</th>
                                            <th>Nome</th>
                                            <th>Username</th>
                                            <th>Status</th>
                                            <th>Tipo</th>
                                            <th>Data de Criação</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for user in users %}
                                        <tr>
                                            <td>{{ user.email }}</td>
                                            <td>{{ user.get_full_name|default:"-" }}</td>
                                            <td>{{ user.username }}</td>
                                            <td>
                                                {% if user.is_active %}
                                                    <span class="badge bg-success">Ativo</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inativo</span>
                                                {% endif %}
                                                {% if user.is_verified %}
                                                    <span class="badge bg-info">Verificado</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if user.is_superuser %}
                                                    <span class="badge bg-danger">Superusuário</span>
                                                {% elif user.is_staff %}
                                                    <span class="badge bg-warning">Staff</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Usuário</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ user.date_joined|date:"d/m/Y H:i" }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="Visualizar">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary" title="Editar">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    {% if not user.is_superuser and user != request.user %}
                                                    <button class="btn btn-outline-danger" title="Deletar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Paginação -->
                            {% if page_obj.has_other_pages %}
                            <nav aria-label="Paginação">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Anterior</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Próximo</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        {% else %}
                            <div class="text-center py-4">
                                <p class="text-muted">Nenhum usuário encontrado.</p>
                                <a href="{% url 'config:user_create' %}" class="btn btn-primary">Criar Primeiro Usuário</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
</body>
</html>
