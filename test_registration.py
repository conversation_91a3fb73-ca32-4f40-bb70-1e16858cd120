#!/usr/bin/env python
"""
Script para testar o sistema de registro
Execute: python test_registration.py
"""

import os
import sys
import django

# Configurar Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from apps.accounts.services.registration_service import RegistrationService
from apps.accounts.repositories.user_repository import DjangoUserRepository
from apps.accounts.notifications.email_notification import EmailNotificationService
from django.contrib.auth import get_user_model

User = get_user_model()

def test_registration():
    """Testa o sistema de registro completo"""
    print("🧪 TESTANDO SISTEMA DE REGISTRO")
    print("=" * 50)
    
    # Dados de teste
    test_data = {
        'email': '<EMAIL>',
        'password': 'senha123456',
        'first_name': '<PERSON>',
        'last_name': '<PERSON>',
        'username': 'joa<PERSON><PERSON><PERSON>'
    }
    
    try:
        # Limpar usuário de teste se existir
        try:
            existing_user = User.objects.get(email=test_data['email'])
            existing_user.delete()
            print(f"🗑️ Usuário de teste removido: {test_data['email']}")
        except User.DoesNotExist:
            pass
        
        # Criar serviços
        from apps.accounts.repositories.verification_repository import DjangoVerificationRepository

        user_repository = DjangoUserRepository()
        verification_repository = DjangoVerificationRepository()
        notification_service = EmailNotificationService()

        registration_service = RegistrationService(
            user_repository=user_repository,
            verification_repository=verification_repository,
            notification_service=notification_service
        )
        
        print(f"📧 Testando registro para: {test_data['email']}")
        
        # Testar registro
        user = registration_service.register_user(
            email=test_data['email'],
            password=test_data['password'],
            first_name=test_data['first_name'],
            last_name=test_data['last_name'],
            username=test_data['username']
        )
        
        print(f"✅ Usuário criado: {user.email}")
        print(f"📝 Nome: {user.get_full_name()}")
        print(f"🔑 Username: {user.username}")
        print(f"✉️ Verificado: {user.is_verified}")
        print(f"🔓 Ativo: {user.is_active}")
        
        # Verificar se o código foi gerado
        if hasattr(user, 'verification_code'):
            print(f"🔢 Código gerado: {user.verification_code}")
        
        # Verificar se há registro de verificação
        from apps.accounts.models import VerificationCode
        try:
            verification = VerificationCode.objects.get(user=user)
            print(f"📋 Código de verificação: {verification.code}")
            print(f"⏰ Expira em: {verification.expires_at}")
            print(f"✅ Válido: {not verification.is_expired()}")
        except VerificationCode.DoesNotExist:
            print("❌ Nenhum código de verificação encontrado!")
        
        print("\n🎉 REGISTRO REALIZADO COM SUCESSO!")
        return True
        
    except Exception as e:
        print(f"❌ ERRO NO REGISTRO: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_verification():
    """Testa o sistema de verificação"""
    print("\n🔍 TESTANDO SISTEMA DE VERIFICAÇÃO")
    print("=" * 50)
    
    try:
        # Buscar usuário de teste
        user = User.objects.get(email='<EMAIL>')
        
        # Buscar código de verificação
        from apps.accounts.models import VerificationCode
        verification = VerificationCode.objects.get(user=user)
        
        print(f"👤 Usuário: {user.email}")
        print(f"🔢 Código: {verification.code}")
        
        # Criar serviços
        from apps.accounts.repositories.verification_repository import DjangoVerificationRepository

        user_repository = DjangoUserRepository()
        verification_repository = DjangoVerificationRepository()
        notification_service = EmailNotificationService()

        registration_service = RegistrationService(
            user_repository=user_repository,
            verification_repository=verification_repository,
            notification_service=notification_service
        )
        
        # Testar confirmação
        result = registration_service.confirm_registration(
            email=user.email,
            code=verification.code
        )
        
        if result:
            print("✅ VERIFICAÇÃO REALIZADA COM SUCESSO!")
            
            # Verificar se o usuário foi ativado
            user.refresh_from_db()
            print(f"✉️ Verificado: {user.is_verified}")
            print(f"🔓 Ativo: {user.is_active}")
            
        else:
            print("❌ FALHA NA VERIFICAÇÃO!")
        
        return result
        
    except Exception as e:
        print(f"❌ ERRO NA VERIFICAÇÃO: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def cleanup():
    """Limpa dados de teste"""
    try:
        user = User.objects.get(email='<EMAIL>')
        user.delete()
        print("🗑️ Dados de teste removidos")
    except User.DoesNotExist:
        pass

def main():
    """Função principal"""
    print("🚀 TESTE COMPLETO DO SISTEMA DE REGISTRO")
    print("=" * 60)
    
    # Testar registro
    registration_ok = test_registration()
    
    if registration_ok:
        # Testar verificação
        verification_ok = test_verification()
        
        print("\n" + "=" * 60)
        print("📊 RESULTADO FINAL:")
        print(f"Registro: {'✅ OK' if registration_ok else '❌ ERRO'}")
        print(f"Verificação: {'✅ OK' if verification_ok else '❌ ERRO'}")
        
        if registration_ok and verification_ok:
            print("\n🎉 SISTEMA DE REGISTRO FUNCIONANDO PERFEITAMENTE!")
        else:
            print("\n⚠️ PROBLEMAS ENCONTRADOS NO SISTEMA!")
    
    else:
        print("\n❌ FALHA NO REGISTRO - NÃO É POSSÍVEL TESTAR VERIFICAÇÃO")
    
    # Limpar dados de teste
    cleanup()

if __name__ == '__main__':
    main()
