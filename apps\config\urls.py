from django.urls import path
from apps.config.views import (
    ConfigDashboardView,
    UserListView,
    UserCreateView,
    UserDetailView,
    UserUpdateView,
    UserDeleteView,
    SystemConfigView,
    SystemConfigListView,
    SystemConfigCreateView,
    SystemConfigUpdateView,
    SystemConfigDeleteView,
)

app_name = 'config'

urlpatterns = [
    # Dashboard
    path('', ConfigDashboardView.as_view(), name='dashboard'),
    
    # Usuários
    path('usuarios/', UserListView.as_view(), name='user_list'),
    path('usuarios/criar/', UserCreateView.as_view(), name='user_create'),
    path('usuarios/<int:user_id>/', UserDetailView.as_view(), name='user_detail'),
    path('usuarios/<int:user_id>/editar/', UserUpdateView.as_view(), name='user_update'),
    path('usuarios/<int:user_id>/deletar/', UserDeleteView.as_view(), name='user_delete'),
    
    # Grupos (TODO)
    # path('grupos/', GroupListView.as_view(), name='group_list'),
    # path('grupos/criar/', GroupCreateView.as_view(), name='group_create'),
    # path('grupos/<int:group_id>/', GroupDetailView.as_view(), name='group_detail'),
    # path('grupos/<int:group_id>/editar/', GroupUpdateView.as_view(), name='group_update'),
    # path('grupos/<int:group_id>/deletar/', GroupDeleteView.as_view(), name='group_delete'),
    
    # Configurações do Sistema
    path('sistema/', SystemConfigView.as_view(), name='system_config'),
    path('sistema/configuracoes/', SystemConfigListView.as_view(), name='system_config_list'),
    path('sistema/configuracoes/criar/', SystemConfigCreateView.as_view(), name='system_config_create'),
    path('sistema/configuracoes/<str:config_key>/editar/', SystemConfigUpdateView.as_view(), name='system_config_update'),
    path('sistema/configuracoes/<str:config_key>/deletar/', SystemConfigDeleteView.as_view(), name='system_config_delete'),
    
    # Logs de Auditoria (TODO)
    # path('logs/', AuditLogListView.as_view(), name='audit_log_list'),
    # path('logs/usuario/<int:user_id>/', UserAuditLogView.as_view(), name='user_audit_logs'),
]
