{% extends 'pages/base.html' %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Artigos</h1>
                <div>
                    <a href="{% url 'articles:search' %}" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>Buscar
                    </a>
                </div>
            </div>
            
            {% if articles %}
                <div class="row g-4">
                    {% for article in articles %}
                    <div class="col-md-6">
                        <div class="card h-100 border-0 shadow-sm">
                            {% if article.featured_image %}
                                <img src="{{ article.featured_image.url }}" class="card-img-top" alt="{{ article.featured_image_alt|default:article.title }}" style="height: 200px; object-fit: cover;">
                            {% endif %}
                            <div class="card-body d-flex flex-column">
                                <div class="mb-2">
                                    {% if article.category %}
                                        <span class="badge" style="background-color: {{ article.category.color }}">
                                            {% if article.category.icon %}<i class="{{ article.category.icon }} me-1"></i>{% endif %}
                                            {{ article.category.name }}
                                        </span>
                                    {% endif %}
                                    {% if article.is_featured %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-star me-1"></i>Destaque
                                        </span>
                                    {% endif %}
                                </div>
                                
                                <h5 class="card-title">
                                    <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                        {{ article.title }}
                                    </a>
                                </h5>
                                
                                <p class="card-text text-muted">{{ article.excerpt|truncatechars:120 }}</p>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ article.author.get_full_name|default:article.author.username }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>{{ article.published_at|date:"d/m/Y" }}
                                        </small>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            {% for tag in article.tags.all|slice:":3" %}
                                                <span class="badge bg-light text-dark me-1">{{ tag.name }}</span>
                                            {% endfor %}
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-eye me-1"></i>{{ article.view_count }}
                                            <i class="fas fa-clock ms-2 me-1"></i>{{ article.reading_time }} min
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Paginação -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Paginação" class="mt-5">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Anterior</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Próximo</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-5x text-muted mb-3"></i>
                    <h3 class="text-muted">Nenhum artigo encontrado</h3>
                    <p class="text-muted">Ainda não há artigos publicados.</p>
                </div>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Artigos em Destaque -->
            {% if featured_articles %}
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-star me-2"></i>Artigos em Destaque</h6>
                </div>
                <div class="card-body">
                    {% for featured in featured_articles %}
                    <div class="d-flex mb-3 {% if not forloop.last %}border-bottom pb-3{% endif %}">
                        {% if featured.featured_image %}
                            <img src="{{ featured.featured_image.url }}" class="me-3 rounded" style="width: 60px; height: 60px; object-fit: cover;" alt="{{ featured.title }}">
                        {% endif %}
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                <a href="{{ featured.get_absolute_url }}" class="text-decoration-none">
                                    {{ featured.title|truncatechars:50 }}
                                </a>
                            </h6>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>{{ featured.published_at|date:"d/m/Y" }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Busca -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-search me-2"></i>Buscar Artigos</h6>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'articles:search' %}">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control" placeholder="Digite sua busca...">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
