from django.views import View
from django.shortcuts import render, redirect
from django.urls import reverse
from django.contrib import messages
from django.core.exceptions import ObjectDoesNotExist
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from apps.accounts.forms.registration import RegistrationForm, VerificationForm
from apps.accounts.services.registration_service import RegistrationService
from apps.accounts.repositories.user_repository import DjangoUserRepository
from apps.accounts.repositories.verification_repository import DjangoVerificationRepository
from apps.accounts.notifications.email_notification import EmailNotificationService

# Importação condicional do django-ratelimit
try:
    from django_ratelimit.decorators import ratelimit
    from django_ratelimit.exceptions import Ratelimited
    RATELIMIT_AVAILABLE = True
except ImportError:
    RATELIMIT_AVAILABLE = False
    # Decorator dummy para quando ratelimit não estiver disponível
    def ratelimit(key=None, rate=None, block=False):
        def decorator(func):
            return func
        return decorator

class RegistrationView(View):
    """View para registro de novos usuários"""
    template_name = 'accounts/registration/register.html'
    form_class = RegistrationForm
    
    def get(self, request):
        """Exibe o formulário de registro"""
        if request.user.is_authenticated:
            messages.info(request, 'Você já está logado.')
            return redirect('home')
            
        form = self.form_class()
        return render(request, self.template_name, {'form': form})
    
    def post(self, request):
        """Processa o formulário de registro"""
        form = self.form_class(request.POST)
        
        if form.is_valid():
            service = RegistrationService(
                user_repository=DjangoUserRepository(),
                verification_repository=DjangoVerificationRepository(),
                notification_service=EmailNotificationService()
            )
            
            try:
                user = service.register_user(
                    email=form.cleaned_data['email'],
                    password=form.cleaned_data['password1'],
                    first_name=form.cleaned_data['first_name'],
                    last_name=form.cleaned_data['last_name'],
                    username=form.cleaned_data['username']
                )
                
                # Armazena email na sessão para a próxima etapa
                request.session['registration_email'] = user.email
                messages.success(
                    request, 
                    'Código de verificação enviado para seu e-mail! '
                    'Verifique sua caixa de entrada.'
                )
                return redirect('accounts:verification')
                
            except ValueError as e:
                form.add_error(None, str(e))
            except Exception as e:
                form.add_error(None, 'Ocorreu um erro durante o registro. Tente novamente.')
                # Logar o erro em produção
        
        return render(request, self.template_name, {'form': form})

class VerificationView(View):
    """View para verificação de e-mail"""
    template_name = 'accounts/registration/verification.html'
    form_class = VerificationForm
    
    def get(self, request):
        """Exibe o formulário de verificação"""
        if 'registration_email' not in request.session:
            messages.warning(request, 'Por favor, complete o registro primeiro.')
            return redirect('accounts:register')
            
        email = request.session['registration_email']
        form = self.form_class(initial={'email': email})
        return render(request, self.template_name, {'form': form, 'email': email})
    
    @ratelimit(key='ip', rate='5/h', block=True)
    def post(self, request):
        """Processa o código de verificação"""
        form = self.form_class(request.POST)
        
        if form.is_valid():
            service = RegistrationService(
                user_repository=DjangoUserRepository(),
                verification_repository=DjangoVerificationRepository(),
                notification_service=EmailNotificationService()
            )
            
            email = form.cleaned_data['email']
            code = form.cleaned_data['code']
            
            try:
                if service.confirm_registration(email, code):
                    # Remove email da sessão após verificação
                    if 'registration_email' in request.session:
                        del request.session['registration_email']
                    
                    messages.success(
                        request, 
                        'Cadastro confirmado com sucesso! Faça login para continuar.'
                    )
                    return redirect('accounts:login')
                else:
                    form.add_error('code', 'Código inválido ou expirado')
            except ObjectDoesNotExist:
                form.add_error('email', 'E-mail não encontrado')
            except Exception as e:
                form.add_error(None, 'Ocorreu um erro durante a verificação. Tente novamente.')
        
        return render(request, self.template_name, {'form': form})