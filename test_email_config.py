#!/usr/bin/env python
"""
Script para testar configurações de email
Execute: python test_email_config.py
"""

import os
import sys
import django

# Configurar Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags

def test_email_settings():
    """Testa as configurações de email"""
    print("🔍 VERIFICANDO CONFIGURAÇÕES DE EMAIL")
    print("=" * 50)
    
    # Verificar configurações básicas
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"DEFAULT_FROM_EMAIL: {getattr(settings, 'DEFAULT_FROM_EMAIL', 'NÃO CONFIGURADO')}")
    
    if hasattr(settings, 'EMAIL_HOST'):
        print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
        print(f"EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', 'NÃO CONFIGURADO')}")
        print(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', 'NÃO CONFIGURADO')}")
        print(f"EMAIL_HOST_USER: {getattr(settings, 'EMAIL_HOST_USER', 'NÃO CONFIGURADO')}")
        print(f"EMAIL_HOST_PASSWORD: {'***' if getattr(settings, 'EMAIL_HOST_PASSWORD', '') else 'NÃO CONFIGURADO'}")
    
    print("\n" + "=" * 50)
    
    # Verificar se é backend de console
    if 'console' in settings.EMAIL_BACKEND.lower():
        print("✅ USANDO CONSOLE BACKEND (DESENVOLVIMENTO)")
        print("📧 Emails serão exibidos no console/terminal")
        return test_console_email()
    
    # Verificar se é backend SMTP
    elif 'smtp' in settings.EMAIL_BACKEND.lower():
        print("📧 USANDO SMTP BACKEND (PRODUÇÃO)")
        return test_smtp_email()
    
    else:
        print(f"⚠️ BACKEND DESCONHECIDO: {settings.EMAIL_BACKEND}")
        return False

def test_console_email():
    """Testa envio com console backend"""
    try:
        print("\n🧪 TESTANDO ENVIO DE EMAIL (CONSOLE)...")
        
        # Testar template
        html_message = render_to_string(
            'accounts/emails/registration_confirmation.html',
            {'email': '<EMAIL>', 'code': '123456'}
        )
        plain_message = strip_tags(html_message)
        
        print("✅ Template renderizado com sucesso")
        
        # Enviar email
        send_mail(
            subject='Teste de Configuração - HAVOC',
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            html_message=html_message,
            fail_silently=False,
        )
        
        print("✅ EMAIL ENVIADO COM SUCESSO!")
        print("📝 Verifique o console/terminal para ver o email")
        return True
        
    except Exception as e:
        print(f"❌ ERRO AO ENVIAR EMAIL: {str(e)}")
        return False

def test_smtp_email():
    """Testa envio com SMTP backend"""
    try:
        print("\n🧪 TESTANDO ENVIO DE EMAIL (SMTP)...")
        
        # Verificar configurações obrigatórias
        required_settings = ['EMAIL_HOST', 'EMAIL_HOST_USER', 'EMAIL_HOST_PASSWORD']
        missing = []
        
        for setting in required_settings:
            if not getattr(settings, setting, ''):
                missing.append(setting)
        
        if missing:
            print(f"❌ CONFIGURAÇÕES FALTANDO: {', '.join(missing)}")
            return False
        
        # Testar template
        html_message = render_to_string(
            'accounts/emails/registration_confirmation.html',
            {'email': '<EMAIL>', 'code': '123456'}
        )
        plain_message = strip_tags(html_message)
        
        print("✅ Template renderizado com sucesso")
        
        # Enviar email de teste
        send_mail(
            subject='Teste de Configuração - HAVOC',
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.EMAIL_HOST_USER],  # Enviar para si mesmo
            html_message=html_message,
            fail_silently=False,
        )
        
        print("✅ EMAIL ENVIADO COM SUCESSO!")
        print(f"📧 Email enviado para: {settings.EMAIL_HOST_USER}")
        return True
        
    except Exception as e:
        print(f"❌ ERRO AO ENVIAR EMAIL: {str(e)}")
        return False

def check_templates():
    """Verifica se os templates de email existem"""
    print("\n🔍 VERIFICANDO TEMPLATES DE EMAIL")
    print("=" * 50)
    
    templates = [
        'accounts/emails/registration_confirmation.html',
        'accounts/emails/password_reset.html',
        'accounts/emails/email_change.html'
    ]
    
    all_exist = True
    
    for template in templates:
        try:
            render_to_string(template, {'email': '<EMAIL>', 'code': '123456'})
            print(f"✅ {template}")
        except Exception as e:
            print(f"❌ {template} - ERRO: {str(e)}")
            all_exist = False
    
    return all_exist

def main():
    """Função principal"""
    print("🚀 TESTE DE CONFIGURAÇÃO DE EMAIL - HAVOC")
    print("=" * 60)
    
    # Verificar templates
    templates_ok = check_templates()
    
    # Verificar configurações
    email_ok = test_email_settings()
    
    print("\n" + "=" * 60)
    print("📊 RESULTADO FINAL:")
    print(f"Templates: {'✅ OK' if templates_ok else '❌ ERRO'}")
    print(f"Email: {'✅ OK' if email_ok else '❌ ERRO'}")
    
    if templates_ok and email_ok:
        print("\n🎉 CONFIGURAÇÃO DE EMAIL FUNCIONANDO!")
        print("✅ O sistema de registro deve funcionar corretamente")
    else:
        print("\n⚠️ PROBLEMAS ENCONTRADOS!")
        print("❌ O sistema de registro pode não funcionar")
        
        if not templates_ok:
            print("🔧 Verifique se os templates de email existem")
        
        if not email_ok:
            print("🔧 Verifique as configurações de email no settings.py")

if __name__ == '__main__':
    main()
