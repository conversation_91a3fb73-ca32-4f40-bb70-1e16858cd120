<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ meta_title|default:"Havoc" }}{% endblock %}</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="{{ meta_description|default:'Sistema de gerenciamento de conteúdo' }}">
    {% if meta_keywords %}<meta name="keywords" content="{{ meta_keywords }}">{% endif %}
    
    <!-- Open Graph -->
    <meta property="og:title" content="{{ meta_title|default:'Havoc' }}">
    <meta property="og:description" content="{{ meta_description|default:'Sistema de gerenciamento de conteúdo' }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'pages:home' %}">
                <i class="fas fa-home me-2"></i>Havoc
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'pages:home' %}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'pages:about' %}">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'pages:contact' %}">Contato</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'pages:page_list' %}">Páginas</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ user.email }}
                            </a>
                            <ul class="dropdown-menu">
                                {% if user.is_staff %}
                                    <li><a class="dropdown-item" href="{% url 'config:dashboard' %}">
                                        <i class="fas fa-cog me-2"></i>Admin
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Sair
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Entrar
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:register' %}">
                                <i class="fas fa-user-plus me-1"></i>Registrar
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    {% if breadcrumbs %}
    <nav aria-label="breadcrumb" class="bg-light">
        <div class="container">
            <ol class="breadcrumb mb-0 py-2">
                {% for breadcrumb in breadcrumbs %}
                    {% if breadcrumb.is_current %}
                        <li class="breadcrumb-item active" aria-current="page">{{ breadcrumb.title }}</li>
                    {% else %}
                        <li class="breadcrumb-item">
                            <a href="{{ breadcrumb.url }}">{{ breadcrumb.title }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
            </ol>
        </div>
    </nav>
    {% endif %}

    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5">
        <div class="container py-4">
            <div class="row">
                <div class="col-md-6">
                    <h5>Havoc</h5>
                    <p class="text-muted">Sistema de gerenciamento de conteúdo</p>
                </div>
                <div class="col-md-6">
                    <h6>Links Úteis</h6>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'pages:about' %}" class="text-light">Sobre</a></li>
                        <li><a href="{% url 'pages:contact' %}" class="text-light">Contato</a></li>
                        <li><a href="{% url 'pages:privacy' %}" class="text-light">Privacidade</a></li>
                        <li><a href="{% url 'pages:terms' %}" class="text-light">Termos</a></li>
                    </ul>
                </div>
            </div>
            <hr class="text-muted">
            <div class="text-center">
                <small>&copy; {% now "Y" %} Havoc. Todos os direitos reservados.</small>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
