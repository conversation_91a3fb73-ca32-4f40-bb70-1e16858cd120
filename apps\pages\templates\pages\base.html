{% load static %}
<!DOCTYPE html>
<html lang="pt-br" class="h-100">
<head>
    {% include 'pages/includes/_head.html' %}
</head>
<body class="d-flex flex-column h-100">
    <!-- Google Tag Manager (noscript) -->
    {% if google_tag_manager_id %}
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id={{ google_tag_manager_id }}" 
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    {% endif %}

    <!-- Skip to main content -->
    <a class="visually-hidden-focusable" href="#main-content">Pular para o conteúdo principal</a>

    <!-- Header -->
    <header>
        {% include 'pages/includes/_nav.html' %}
    </header>

    <!-- Messages -->
    {% if messages %}
    <div class="container-fluid">
        <div class="container mt-3">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {% if message.tags == 'error' %}
                    <i class="fas fa-exclamation-triangle me-2"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-circle me-2"></i>
                {% elif message.tags == 'success' %}
                    <i class="fas fa-check-circle me-2"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle me-2"></i>
                {% endif %}
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Main Content -->
    <main id="main-content" class="flex-shrink-0">
        {% block content %}
        <!-- Default content if no content block is provided -->
        <div class="container my-5">
            <div class="row">
                <div class="col-12 text-center">
                    <h1>Bem-vindo ao Havoc</h1>
                    <p class="lead">Sistema de gerenciamento de conteúdo moderno</p>
                </div>
            </div>
        </div>
        {% endblock %}
    </main>

    <!-- Footer -->
    {% include 'pages/includes/_footer.html' %}

    <!-- JavaScript -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" 
            integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" 
            crossorigin="anonymous"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Extra JavaScript -->
    {% block extra_js %}{% endblock %}

    <!-- Structured Data -->
    {% block structured_data %}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Havoc",
        "description": "Sistema de gerenciamento de conteúdo moderno",
        "url": "{{ request.build_absolute_uri }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.build_absolute_uri }}{% url 'articles:search' %}?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    {% endblock %}
</body>
</html>
