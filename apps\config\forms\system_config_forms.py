from django import forms
from django.core.validators import MinLengthValidator
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, Reset, HTML, Div
from crispy_forms.bootstrap import FormActions
from apps.config.models import SystemConfiguration
import json


class SystemConfigForm(forms.ModelForm):
    """Formulário para configurações do sistema"""
    
    class Meta:
        model = SystemConfiguration
        fields = ['key', 'value', 'description', 'is_active']
        widgets = {
            'key': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'ex: site_name, email_settings, etc.',
                'pattern': '[a-zA-Z0-9_]+',
                'title': 'Apenas letras, números e underscore'
            }),
            'value': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': '<PERSON><PERSON> da configuração (pode ser JSON)',
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Descrição da configuração'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Labels personalizados
        self.fields['key'].label = 'Chave da Configuração'
        self.fields['value'].label = 'Valor'
        self.fields['description'].label = 'Descrição'
        self.fields['is_active'].label = 'Configuração Ativa'
        
        # Help texts
        self.fields['key'].help_text = 'Identificador único da configuração (apenas letras, números e underscore)'
        self.fields['value'].help_text = 'Valor da configuração. Pode ser texto simples ou JSON válido'
        self.fields['description'].help_text = 'Descrição detalhada do que esta configuração controla'
        self.fields['is_active'].help_text = 'Desmarque para desativar temporariamente esta configuração'
        
        # Validações adicionais
        self.fields['key'].validators.append(MinLengthValidator(3))
        
        # Crispy Forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.attrs = {'novalidate': ''}
        
        self.helper.layout = Layout(
            Fieldset(
                'Informações da Configuração',
                Row(
                    Column('key', css_class='form-group col-md-6 mb-3'),
                    Column(
                        Div(
                            HTML('<label class="form-label">Tipo de Valor</label>'),
                            HTML('''
                                <div class="btn-group w-100" role="group" id="valueTypeGroup">
                                    <input type="radio" class="btn-check" name="value_type" id="value_type_text" value="text" checked>
                                    <label class="btn btn-outline-primary" for="value_type_text">
                                        <i class="fas fa-font me-1"></i>Texto
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="value_type" id="value_type_json" value="json">
                                    <label class="btn btn-outline-primary" for="value_type_json">
                                        <i class="fas fa-code me-1"></i>JSON
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="value_type" id="value_type_boolean" value="boolean">
                                    <label class="btn btn-outline-primary" for="value_type_boolean">
                                        <i class="fas fa-toggle-on me-1"></i>Boolean
                                    </label>
                                </div>
                            '''),
                            css_class='form-group mb-3'
                        ),
                        css_class='col-md-6'
                    ),
                    css_class='form-row'
                ),
                'value',
                'description',
                Row(
                    Column('is_active', css_class='form-group col-md-6 mb-3'),
                    Column(
                        HTML('''
                            <div class="form-group">
                                <label class="form-label">Validação JSON</label>
                                <div id="jsonValidation" class="alert alert-info d-none">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <span id="jsonValidationText">Digite um JSON válido</span>
                                </div>
                            </div>
                        '''),
                        css_class='col-md-6'
                    ),
                    css_class='form-row'
                ),
                css_class='border-bottom pb-3 mb-4'
            ),
            
            # Exemplos e dicas
            Fieldset(
                'Exemplos e Dicas',
                HTML('''
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-primary">Texto Simples</h6>
                            <div class="bg-light p-2 rounded small">
                                <code>Meu Site</code><br>
                                <code><EMAIL></code><br>
                                <code>https://exemplo.com</code>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">JSON Object</h6>
                            <div class="bg-light p-2 rounded small">
                                <code>{<br>
                                &nbsp;&nbsp;"host": "smtp.gmail.com",<br>
                                &nbsp;&nbsp;"port": 587,<br>
                                &nbsp;&nbsp;"use_tls": true<br>
                                }</code>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">JSON Array</h6>
                            <div class="bg-light p-2 rounded small">
                                <code>[<br>
                                &nbsp;&nbsp;"<EMAIL>",<br>
                                &nbsp;&nbsp;"<EMAIL>"<br>
                                ]</code>
                            </div>
                        </div>
                    </div>
                '''),
                css_class='mb-4'
            ),
            
            FormActions(
                Submit('submit', 'Salvar Configuração', css_class='btn btn-primary btn-lg'),
                Reset('reset', 'Limpar', css_class='btn btn-outline-secondary btn-lg'),
                HTML('<a href="{% url \'config:system_config_list\' %}" class="btn btn-outline-danger btn-lg">Cancelar</a>'),
                css_class='d-flex justify-content-between'
            )
        )

    def clean_value(self):
        """Valida o valor da configuração"""
        value = self.cleaned_data.get('value')
        
        if not value:
            return value
            
        # Tenta validar como JSON se parecer com JSON
        value_stripped = value.strip()
        if (value_stripped.startswith('{') and value_stripped.endswith('}')) or \
           (value_stripped.startswith('[') and value_stripped.endswith(']')):
            try:
                json.loads(value)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f'JSON inválido: {str(e)}')
        
        return value

    def clean_key(self):
        """Valida a chave da configuração"""
        key = self.cleaned_data.get('key')
        
        if not key:
            return key
            
        # Verifica se contém apenas caracteres válidos
        if not key.replace('_', '').replace('-', '').isalnum():
            raise forms.ValidationError('A chave deve conter apenas letras, números, underscore e hífen')
        
        # Verifica se já existe (apenas para criação)
        if not self.instance.pk:
            if SystemConfiguration.objects.filter(key=key).exists():
                raise forms.ValidationError('Já existe uma configuração com esta chave')
        
        return key.lower()  # Converte para minúsculo


class SystemConfigSearchForm(forms.Form):
    """Formulário de busca para configurações do sistema"""
    
    query = forms.CharField(
        label='Buscar',
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Buscar por chave, descrição ou valor...',
            'autocomplete': 'off'
        })
    )
    
    is_active = forms.ChoiceField(
        label='Status',
        choices=[
            ('', 'Todos'),
            ('true', 'Ativos'),
            ('false', 'Inativos'),
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    category = forms.CharField(
        label='Categoria',
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ex: email, site, api...',
            'list': 'categories'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'row g-3'
        self.helper.disable_csrf = True
        
        self.helper.layout = Layout(
            Row(
                Column('query', css_class='col-md-6'),
                Column('is_active', css_class='col-md-3'),
                Column('category', css_class='col-md-3'),
                css_class='align-items-end'
            ),
            Row(
                Column(
                    Submit('submit', 'Filtrar', css_class='btn btn-primary'),
                    HTML('<a href="?" class="btn btn-outline-secondary ms-2">Limpar</a>'),
                    css_class='col-12 text-end'
                )
            )
        )

    def clean_is_active(self):
        """Converte string para boolean"""
        value = self.cleaned_data.get('is_active')
        if value == 'true':
            return True
        elif value == 'false':
            return False
        return None


class SystemConfigBulkForm(forms.Form):
    """Formulário para operações em lote"""
    
    ACTION_CHOICES = [
        ('activate', 'Ativar selecionados'),
        ('deactivate', 'Desativar selecionados'),
        ('delete', 'Deletar selecionados'),
        ('export', 'Exportar selecionados'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    selected_configs = forms.CharField(
        widget=forms.HiddenInput()
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'd-inline'
        
        self.helper.layout = Layout(
            Row(
                Column('action', css_class='col-auto'),
                Column(
                    Submit('submit', 'Executar', css_class='btn btn-warning'),
                    css_class='col-auto'
                ),
                'selected_configs'
            )
        )


class SystemConfigImportForm(forms.Form):
    """Formulário para importar configurações"""
    
    config_file = forms.FileField(
        label='Arquivo de Configurações',
        help_text='Arquivo JSON com as configurações a serem importadas',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.json'
        })
    )
    
    overwrite_existing = forms.BooleanField(
        label='Sobrescrever configurações existentes',
        required=False,
        help_text='Marque para sobrescrever configurações que já existem',
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        
        self.helper.layout = Layout(
            'config_file',
            'overwrite_existing',
            FormActions(
                Submit('submit', 'Importar', css_class='btn btn-success'),
                HTML('<a href="{% url \'config:system_config_list\' %}" class="btn btn-secondary">Cancelar</a>')
            )
        )
    
    def clean_config_file(self):
        """Valida o arquivo de configurações"""
        file = self.cleaned_data.get('config_file')
        
        if not file:
            return file
            
        if not file.name.endswith('.json'):
            raise forms.ValidationError('O arquivo deve ter extensão .json')
        
        try:
            content = file.read().decode('utf-8')
            json.loads(content)
            file.seek(0)  # Reset file pointer
        except (UnicodeDecodeError, json.JSONDecodeError) as e:
            raise forms.ValidationError(f'Arquivo JSON inválido: {str(e)}')
        
        return file
