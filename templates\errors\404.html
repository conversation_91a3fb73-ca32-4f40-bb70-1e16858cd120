{% extends 'base.html' %}

{% block title %}Página Não Encontrada - {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mt-5">
                <!-- Ícone de erro -->
                <div class="mb-4">
                    <i class="fas fa-search text-warning" style="font-size: 6rem;"></i>
                </div>
                
                <!-- Título principal -->
                <h1 class="display-4 text-warning mb-3">404</h1>
                <h2 class="h3 mb-4">Página Não Encontrada</h2>
                
                <!-- Mensagem principal -->
                <div class="alert alert-info border-0 shadow-sm mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle text-info me-3 fa-2x"></i>
                        <div class="text-start">
                            <h5 class="alert-heading mb-2">Oops! Esta página não existe</h5>
                            <p class="mb-0">
                                A página que você está procurando em <code>{{ requested_path }}</code> não foi encontrada.
                                Ela pode ter sido movida, removida ou você digitou o endereço incorretamente.
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Sugestões -->
                <div class="card border-0 bg-light mb-4">
                    <div class="card-body">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-lightbulb me-2"></i>O que você pode fazer?
                        </h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <i class="fas fa-spell-check text-primary fa-2x mb-2"></i>
                                <p class="small mb-0"><strong>Verificar a URL</strong></p>
                                <small class="text-muted">Confira se não há erros de digitação</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <i class="fas fa-arrow-left text-success fa-2x mb-2"></i>
                                <p class="small mb-0"><strong>Voltar</strong></p>
                                <small class="text-muted">Use o botão voltar do navegador</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <i class="fas fa-home text-info fa-2x mb-2"></i>
                                <p class="small mb-0"><strong>Ir ao Início</strong></p>
                                <small class="text-muted">Comece pela página principal</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Ações disponíveis -->
                <div class="row justify-content-center">
                    <div class="col-md-4 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-home text-primary fa-2x mb-3"></i>
                                <h6>Página Inicial</h6>
                                <p class="text-muted small mb-3">
                                    Voltar para a página principal
                                </p>
                                <a href="{{ home_url }}" class="btn btn-primary">
                                    <i class="fas fa-home me-2"></i>Início
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    {% if user_is_authenticated %}
                    <div class="col-md-4 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-user-circle text-success fa-2x mb-3"></i>
                                <h6>Meu Perfil</h6>
                                <p class="text-muted small mb-3">
                                    Ir para sua área pessoal
                                </p>
                                <a href="{{ profile_url }}" class="btn btn-success">
                                    <i class="fas fa-user me-2"></i>Perfil
                                </a>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="col-md-4 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-sign-in-alt text-info fa-2x mb-3"></i>
                                <h6>Fazer Login</h6>
                                <p class="text-muted small mb-3">
                                    Entrar no sistema
                                </p>
                                <a href="{{ login_url }}" class="btn btn-info">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border-secondary">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-left text-secondary fa-2x mb-3"></i>
                                <h6>Voltar</h6>
                                <p class="text-muted small mb-3">
                                    Retornar à página anterior
                                </p>
                                <button onclick="history.back()" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Voltar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Páginas populares -->
                <div class="mt-4">
                    <h6 class="text-muted mb-3">Páginas Populares</h6>
                    <div class="d-flex flex-wrap justify-content-center gap-2">
                        <a href="{{ home_url }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-home me-1"></i>Início
                        </a>
                        {% if user_is_authenticated %}
                        <a href="{{ profile_url }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user me-1"></i>Perfil
                        </a>
                        {% else %}
                        <a href="{{ login_url }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Informações de contato -->
                <div class="mt-4">
                    <p class="text-muted">
                        <i class="fas fa-question-circle me-1"></i>
                        Se o problema persistir, entre em contato com o suporte.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.alert {
    border-radius: 15px;
}

.fa-search {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    color: #e83e8c;
    font-size: 0.9em;
}

/* Responsividade */
@media (max-width: 768px) {
    .display-4 {
        font-size: 3rem;
    }
    
    .fa-search {
        font-size: 4rem !important;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animação de entrada
    const container = document.querySelector('.container');
    if (container) {
        container.style.opacity = '0';
        container.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            container.style.transition = 'all 0.5s ease';
            container.style.opacity = '1';
            container.style.transform = 'translateY(0)';
        }, 100);
    }
    
    // Auto-focus no botão de início
    const homeBtn = document.querySelector('a[href*="home"], a[href="/"]');
    if (homeBtn) {
        setTimeout(() => {
            homeBtn.focus();
        }, 500);
    }
    
    // Adicionar funcionalidade de busca rápida
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K para busca rápida
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            // Implementar busca rápida se necessário
            console.log('Busca rápida ativada');
        }
    });
});
</script>
{% endblock %}
