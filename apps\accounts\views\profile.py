from django.views import View
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth import get_user_model

User = get_user_model()

class UserProfileView(LoginRequiredMixin, View):
    """View para exibir perfil do usuário"""
    template_name = 'accounts/profile/profile.html'
    login_url = '/accounts/login/'
    
    def get(self, request):
        """Exibe o perfil do usuário"""
        user = request.user
        return render(request, self.template_name, {'profile_user': user})

class UserUpdateView(LoginRequiredMixin, View):
    """View para editar perfil do usuário"""
    template_name = 'accounts/profile/edit.html'
    login_url = '/accounts/login/'
    
    def get(self, request):
        """Exibe o formulário de edição"""
        user = request.user
        return render(request, self.template_name, {'profile_user': user})

    def post(self, request):
        """Processa a edição do perfil"""
        user = request.user
        
        # Atualiza os campos básicos
        first_name = request.POST.get('first_name', '').strip()
        last_name = request.POST.get('last_name', '').strip()
        
        if first_name:
            user.first_name = first_name
        if last_name:
            user.last_name = last_name
        
        try:
            user.save()
            messages.success(request, 'Perfil atualizado com sucesso!')
            return redirect('accounts:profile')
        except Exception as e:
            messages.error(request, 'Erro ao atualizar perfil. Tente novamente.')

        return render(request, self.template_name, {'profile_user': user})
