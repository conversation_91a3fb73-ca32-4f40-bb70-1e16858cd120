{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Login - {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm mt-5">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>Entrar
                    </h4>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'success' %}
                                    <i class="fas fa-check-circle me-2"></i>
                                {% elif message.tags == 'error' %}
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                {% else %}
                                    <i class="fas fa-info-circle me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <div class="text-center mb-4">
                        <p class="text-muted">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            Digite suas credenciais para acessar o sistema
                        </p>
                    </div>

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="id_email" class="form-label">E-mail</label>
                            <input type="email" class="form-control" id="id_email" name="email"
                                   placeholder="Digite seu e-mail" autocomplete="email" required>
                            <div class="invalid-feedback">
                                Por favor, digite seu e-mail.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="id_password" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="id_password" name="password"
                                   placeholder="Digite sua senha" autocomplete="current-password" required>
                            <div class="invalid-feedback">
                                Por favor, digite sua senha.
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                Lembrar de mim
                            </label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Entrar
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-2">
                            <a href="{% url 'accounts:password_reset' %}" class="text-decoration-none">
                                Esqueceu sua senha?
                            </a>
                        </p>
                        <p class="mb-0">
                            Não tem uma conta? 
                            <a href="{% url 'accounts:register' %}" class="text-decoration-none">
                                Registre-se
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.alert {
    border: none;
    border-radius: 10px;
}

.card {
    border: none;
    border-radius: 15px;
}

.btn-primary {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus no campo de email
    const emailField = document.querySelector('input[name="email"]');
    if (emailField) {
        emailField.focus();
    }

    // Elementos do formulário
    const passwordField = document.querySelector('input[name="password"]');
    const form = document.querySelector('form');

    // Validação de email
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const email = this.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            this.classList.remove('is-valid', 'is-invalid');

            if (email && emailRegex.test(email)) {
                this.classList.add('is-valid');
            } else if (email) {
                this.classList.add('is-invalid');
            }
        });
    }

    // Feedback visual no envio do formulário
    if (form) {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Entrando...';
                submitBtn.disabled = true;
            }
        });
    }

    // Auto-dismiss de alertas após 5 segundos
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentElement) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 150);
            }
        }, 5000);
    });

    // Enter no campo de senha submete o formulário
    if (passwordField) {
        passwordField.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                form.submit();
            }
        });
    }
});
</script>
{% endblock %}
