{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Login - {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm mt-5">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>Entrar
                    </h4>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="id_username" class="form-label">Usu<PERSON>rio ou E-mail</label>
                            <input type="text" class="form-control" id="id_username" name="username" 
                                   placeholder="Digite seu usuário ou e-mail" required>
                            <div class="invalid-feedback">
                                Por favor, digite seu usuário ou e-mail.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="id_password" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="id_password" name="password" 
                                   placeholder="Digite sua senha" required>
                            <div class="invalid-feedback">
                                Por favor, digite sua senha.
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                Lembrar de mim
                            </label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Entrar
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-2">
                            <a href="{% url 'accounts:password_reset' %}" class="text-decoration-none">
                                Esqueceu sua senha?
                            </a>
                        </p>
                        <p class="mb-0">
                            Não tem uma conta? 
                            <a href="{% url 'accounts:register' %}" class="text-decoration-none">
                                Registre-se
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
