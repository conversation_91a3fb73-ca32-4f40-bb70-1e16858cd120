<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criar <PERSON>figuraç<PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'config:dashboard' %}">Config Admin</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{% url 'config:user_list' %}">Usuários</a>
                <a class="nav-link" href="{% url 'accounts:logout' %}">Sair</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Criar Usuário</h1>
                    <a href="{% url 'config:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Dados do Usuário</h5>
                    </div>
                    <div class="card-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.username.id_for_label }}" class="form-label">{{ form.username.label }}</label>
                                    {{ form.username }}
                                    {% if form.username.errors %}
                                        <div class="text-danger small">{{ form.username.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger small">{{ form.first_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger small">{{ form.last_name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.password1.id_for_label }}" class="form-label">{{ form.password1.label }}</label>
                                    {{ form.password1 }}
                                    {% if form.password1.errors %}
                                        <div class="text-danger small">{{ form.password1.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.password2.id_for_label }}" class="form-label">{{ form.password2.label }}</label>
                                    {{ form.password2 }}
                                    {% if form.password2.errors %}
                                        <div class="text-danger small">{{ form.password2.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <hr>

                            <h6>Permissões</h6>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            {{ form.is_active.label }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        {{ form.is_staff }}
                                        <label class="form-check-label" for="{{ form.is_staff.id_for_label }}">
                                            {{ form.is_staff.label }}
                                        </label>
                                        <small class="form-text text-muted">{{ form.is_staff.help_text }}</small>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        {{ form.is_superuser }}
                                        <label class="form-check-label" for="{{ form.is_superuser.id_for_label }}">
                                            {{ form.is_superuser.label }}
                                        </label>
                                        <small class="form-text text-muted">{{ form.is_superuser.help_text }}</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <h6>Grupos</h6>
                            <div class="mb-3">
                                {% for group in form.groups %}
                                    <div class="form-check">
                                        {{ group.tag }}
                                        <label class="form-check-label" for="{{ group.id_for_label }}">
                                            {{ group.choice_label }}
                                        </label>
                                    </div>
                                {% endfor %}
                                {% if form.groups.errors %}
                                    <div class="text-danger small">{{ form.groups.errors }}</div>
                                {% endif %}
                            </div>

                            <div class="d-flex justify-content-end gap-2">
                                <a href="{% url 'config:user_list' %}" class="btn btn-secondary">Cancelar</a>
                                <button type="submit" class="btn btn-primary">Criar Usuário</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Ajuda</h6>
                    </div>
                    <div class="card-body">
                        <h6>Tipos de Usuário:</h6>
                        <ul class="small">
                            <li><strong>Ativo:</strong> Usuário pode fazer login</li>
                            <li><strong>Staff:</strong> Acesso ao Django Admin</li>
                            <li><strong>Superusuário:</strong> Todas as permissões</li>
                        </ul>

                        <h6>Grupos Disponíveis:</h6>
                        <ul class="small">
                            <li><strong>Administradores:</strong> Acesso total</li>
                            <li><strong>Gerentes:</strong> Gerenciar usuários</li>
                            <li><strong>Operadores:</strong> Operações básicas</li>
                            <li><strong>Usuários:</strong> Acesso limitado</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
</body>
</html>
