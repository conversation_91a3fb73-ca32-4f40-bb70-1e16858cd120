{% extends 'pages/base.html' %}
{% load crispy_forms_tags %}

{% block title %}Meu Perfil - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header do Perfil -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">
                        <i class="fas fa-user me-2 text-primary"></i>Meu Perfil
                    </h1>
                    <p class="text-muted mb-0">Visualize e gerencie suas informações pessoais</p>
                </div>
                <div>
                    <a href="{% url 'accounts:settings' %}" class="btn btn-primary">
                        <i class="fas fa-cog me-1"></i>Configurações
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Card Principal do Perfil -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body text-center p-4">
                    <!-- Avatar -->
                    <div class="mb-4">
                        <div class="position-relative d-inline-block">
                            <img src="{{ profile_user.get_avatar_url }}"
                                 alt="Avatar de {{ profile_user.get_full_name|default:profile_user.username }}"
                                 class="rounded-circle border border-3 border-light shadow"
                                 width="150" height="150"
                                 style="object-fit: cover;">

                            <!-- Badge de Status -->
                            {% if profile_user.is_verified %}
                                <span class="position-absolute bottom-0 end-0 bg-success rounded-circle p-2"
                                      data-bs-toggle="tooltip" title="Conta Verificada">
                                    <i class="fas fa-check text-white"></i>
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Nome e Email -->
                    <h4 class="mb-1">{{ profile_user.get_full_name|default:profile_user.username }}</h4>
                    <p class="text-muted mb-3">{{ profile_user.email }}</p>

                    <!-- Bio -->
                    {% if profile_user.bio %}
                        <p class="text-muted small mb-3">{{ profile_user.bio }}</p>
                    {% endif %}

                    <!-- Localização -->
                    {% if profile_user.location %}
                        <p class="text-muted small mb-3">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ profile_user.location }}
                        </p>
                    {% endif %}

                    <!-- Botões de Ação -->
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:settings' %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Editar Perfil
                        </a>
                    </div>
                </div>
            </div>

            <!-- Card de Estatísticas -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Estatísticas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-primary mb-0">{{ profile_user.authored_articles.count }}</h5>
                                <small class="text-muted">Artigos</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success mb-0">{{ profile_user.comments.count }}</h5>
                            <small class="text-muted">Comentários</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações Detalhadas -->
        <div class="col-lg-8">
            <!-- Informações Pessoais -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-user-circle me-2"></i>Informações Pessoais
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Nome</label>
                            <p class="mb-0">{{ profile_user.first_name|default:"Não informado" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Sobrenome</label>
                            <p class="mb-0">{{ profile_user.last_name|default:"Não informado" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">E-mail</label>
                            <p class="mb-0">
                                {{ profile_user.email }}
                                {% if profile_user.is_verified %}
                                    <i class="fas fa-check-circle text-success ms-1" data-bs-toggle="tooltip" title="E-mail verificado"></i>
                                {% else %}
                                    <i class="fas fa-exclamation-circle text-warning ms-1" data-bs-toggle="tooltip" title="E-mail não verificado"></i>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Username</label>
                            <p class="mb-0">{{ profile_user.username }}</p>
                        </div>
                        {% if profile_user.phone %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Telefone</label>
                            <p class="mb-0">{{ profile_user.phone }}</p>
                        </div>
                        {% endif %}
                        {% if profile_user.birth_date %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Data de Nascimento</label>
                            <p class="mb-0">{{ profile_user.birth_date|date:"d/m/Y" }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Informações da Conta -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Informações da Conta
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Membro desde</label>
                            <p class="mb-0">{{ profile_user.date_joined|date:"d/m/Y" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Último acesso</label>
                            <p class="mb-0">{{ profile_user.last_login|date:"d/m/Y H:i"|default:"Nunca" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Status da Conta</label>
                            <p class="mb-0">
                                {% if profile_user.is_active %}
                                    <span class="badge bg-success">Ativa</span>
                                {% else %}
                                    <span class="badge bg-danger">Inativa</span>
                                {% endif %}

                                {% if profile_user.is_verified %}
                                    <span class="badge bg-primary ms-1">Verificada</span>
                                {% else %}
                                    <span class="badge bg-warning ms-1">Não Verificada</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Tipo de Usuário</label>
                            <p class="mb-0">
                                {% if profile_user.is_superuser %}
                                    <span class="badge bg-danger">Superusuário</span>
                                {% elif profile_user.is_staff %}
                                    <span class="badge bg-warning">Staff</span>
                                {% else %}
                                    <span class="badge bg-secondary">Usuário</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Atividade Recente -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Atividade Recente
                    </h6>
                </div>
                <div class="card-body">
                    {% if profile_user.authored_articles.exists %}
                        <h6 class="text-muted small mb-3">Últimos Artigos</h6>
                        {% for article in profile_user.authored_articles.all|slice:":3" %}
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-file-alt text-primary me-2"></i>
                                <div>
                                    <a href="{{ article.get_absolute_url }}" class="text-decoration-none">{{ article.title }}</a>
                                    <small class="text-muted d-block">{{ article.created_at|date:"d/m/Y" }}</small>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <p class="mb-0">Nenhum artigo publicado ainda</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.75em;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
