{% extends 'base.html' %}

{% block title %}Meu Perfil - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>Meu Perfil
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                {% if profile_user.profile_picture %}
                                    <img src="{{ profile_user.profile_picture.url }}" alt="Foto do perfil" class="rounded-circle" width="120" height="120">
                                {% else %}
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 120px; height: 120px;">
                                        <i class="fas fa-user fa-3x text-muted"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <h5>{{ profile_user.get_full_name|default:profile_user.username }}</h5>
                            <p class="text-muted">{{ profile_user.email }}</p>
                        </div>
                        <div class="col-md-8">
                            <h6>Informações Pessoais</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Nome:</strong></td>
                                    <td>{{ profile_user.first_name|default:"Não informado" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Sobrenome:</strong></td>
                                    <td>{{ profile_user.last_name|default:"Não informado" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>E-mail:</strong></td>
                                    <td>{{ profile_user.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Usuário:</strong></td>
                                    <td>{{ profile_user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Membro desde:</strong></td>
                                    <td>{{ profile_user.date_joined|date:"d/m/Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Último acesso:</strong></td>
                                    <td>{{ profile_user.last_login|date:"d/m/Y H:i"|default:"Nunca" }}</td>
                                </tr>
                            </table>
                            
                            <div class="mt-4">
                                <a href="{% url 'accounts:settings' %}" class="btn btn-primary">
                                    <i class="fas fa-edit me-2"></i>Editar Perfil
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
