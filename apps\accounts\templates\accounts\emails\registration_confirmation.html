<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmação de Cadastro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .title {
            color: #28a745;
            font-size: 24px;
            margin-bottom: 10px;
        }
        .code-container {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin: 25px 0;
        }
        .code {
            font-size: 36px;
            font-weight: bold;
            letter-spacing: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .code-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚀 HAVOC</div>
            <h1 class="title">Bem-vindo!</h1>
            <p>Confirme seu cadastro para ativar sua conta</p>
        </div>

        <div class="content">
            <p>Olá!</p>
            
            <p>Obrigado por se cadastrar em nossa plataforma! Para completar seu registro e ativar sua conta, use o código de verificação abaixo:</p>

            <div class="code-container">
                <div class="code-label">SEU CÓDIGO DE VERIFICAÇÃO</div>
                <div class="code">{{ code }}</div>
                <div style="font-size: 12px; opacity: 0.8;">Digite este código na página de verificação</div>
            </div>

            <div class="instructions">
                <h3 style="margin-top: 0; color: #007bff;">📋 Como usar o código:</h3>
                <ol>
                    <li>Volte para a página de verificação</li>
                    <li>Digite o código <span class="highlight">{{ code }}</span> no campo indicado</li>
                    <li>Clique em "Verificar" para ativar sua conta</li>
                    <li>Faça login e comece a usar a plataforma!</li>
                </ol>
            </div>

            <div class="warning">
                <strong>⚠️ Importante:</strong>
                <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                    <li>Este código é válido por <strong>15 minutos</strong></li>
                    <li>Use apenas uma vez</li>
                    <li>Não compartilhe com ninguém</li>
                    <li>Se não foi você quem se cadastrou, ignore este email</li>
                </ul>
            </div>

            <p style="margin-top: 25px;">
                <strong>Email cadastrado:</strong> <span class="highlight">{{ email }}</span>
            </p>

            <p>Se você não conseguir usar o código, pode solicitar um novo na página de verificação.</p>
        </div>

        <div class="footer">
            <p><strong>HAVOC Platform</strong></p>
            <p>Este é um email automático, não responda.</p>
            <p style="font-size: 12px; margin-top: 15px;">
                Se você não se cadastrou em nossa plataforma, pode ignorar este email com segurança.
            </p>
        </div>
    </div>
</body>
</html>
