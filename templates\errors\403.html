{% extends 'base.html' %}

{% block title %}Acesso Negado - {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mt-5">
                <!-- Ícone de erro -->
                <div class="mb-4">
                    <i class="fas fa-shield-alt text-danger" style="font-size: 6rem;"></i>
                </div>
                
                <!-- Título principal -->
                <h1 class="display-4 text-danger mb-3">403</h1>
                <h2 class="h3 mb-4">Acesso Negado</h2>
                
                <!-- Mensagem contextual -->
                <div class="alert alert-warning border-0 shadow-sm mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle text-warning me-3 fa-2x"></i>
                        <div class="text-start">
                            <h5 class="alert-heading mb-2">Você não tem permissão para acessar {{ area_name }}</h5>
                            <p class="mb-0">
                                {% if user_is_authenticated %}
                                    <strong>{{ user_username }}</strong>, sua conta não possui as permissões necessárias para acessar {{ area_description }}.
                                {% else %}
                                    Você precisa fazer login para acessar {{ area_description }}.
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Informações específicas por tipo de área -->
                {% if area_type == 'config' %}
                <div class="card border-0 bg-light mb-4">
                    <div class="card-body">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-info-circle me-2"></i>Quem pode acessar o Painel de Configurações?
                        </h6>
                        <div class="row text-center">
                            <div class="col-md-4 mb-2">
                                <i class="fas fa-crown text-warning fa-2x mb-2"></i>
                                <p class="small mb-0"><strong>Superusuários</strong></p>
                                <small class="text-muted">Acesso total</small>
                            </div>
                            <div class="col-md-4 mb-2">
                                <i class="fas fa-user-tie text-primary fa-2x mb-2"></i>
                                <p class="small mb-0"><strong>Staff</strong></p>
                                <small class="text-muted">Acesso limitado</small>
                            </div>
                            <div class="col-md-4 mb-2">
                                <i class="fas fa-users-cog text-success fa-2x mb-2"></i>
                                <p class="small mb-0"><strong>Administradores</strong></p>
                                <small class="text-muted">Grupo específico</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% elif area_type == 'admin' %}
                <div class="card border-0 bg-light mb-4">
                    <div class="card-body">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-info-circle me-2"></i>Django Admin
                        </h6>
                        <p class="text-muted small">
                            O Django Admin é uma interface administrativa avançada que requer permissões especiais.
                            Entre em contato com um administrador se precisar de acesso.
                        </p>
                    </div>
                </div>
                {% endif %}
                
                <!-- Ações disponíveis -->
                <div class="row justify-content-center">
                    {% if not user_is_authenticated %}
                    <!-- Se não está logado -->
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-sign-in-alt text-primary fa-2x mb-3"></i>
                                <h6>Fazer Login</h6>
                                <p class="text-muted small mb-3">
                                    Entre com seu e-mail ou nome de usuário para acessar o sistema
                                </p>
                                <a href="{{ login_url }}" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                                </a>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <!-- Se está logado mas sem permissão -->
                    <div class="col-md-6 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-user-circle text-info fa-2x mb-3"></i>
                                <h6>Meu Perfil</h6>
                                <p class="text-muted small mb-3">
                                    Voltar para sua área pessoal
                                </p>
                                <a href="{{ profile_url }}" class="btn btn-info">
                                    <i class="fas fa-user me-2"></i>Ver Perfil
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="col-md-6 mb-3">
                        <div class="card border-secondary">
                            <div class="card-body text-center">
                                <i class="fas fa-home text-secondary fa-2x mb-3"></i>
                                <h6>Página Inicial</h6>
                                <p class="text-muted small mb-3">
                                    Voltar para a página principal do site
                                </p>
                                <a href="{{ home_url }}" class="btn btn-secondary">
                                    <i class="fas fa-home me-2"></i>Início
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Informações de contato -->
                <div class="mt-4">
                    <p class="text-muted">
                        <i class="fas fa-question-circle me-1"></i>
                        Precisa de ajuda? Entre em contato com o suporte ou um administrador.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.alert {
    border-radius: 15px;
}

.fa-shield-alt {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .display-4 {
        font-size: 3rem;
    }
    
    .fa-shield-alt {
        font-size: 4rem !important;
    }
    
    .card-body {
        padding: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animação de entrada
    const container = document.querySelector('.container');
    if (container) {
        container.style.opacity = '0';
        container.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            container.style.transition = 'all 0.5s ease';
            container.style.opacity = '1';
            container.style.transform = 'translateY(0)';
        }, 100);
    }
    
    // Auto-focus no botão de login se disponível
    const loginBtn = document.querySelector('a[href*="login"]');
    if (loginBtn) {
        setTimeout(() => {
            loginBtn.focus();
        }, 500);
    }
});
</script>
{% endblock %}
