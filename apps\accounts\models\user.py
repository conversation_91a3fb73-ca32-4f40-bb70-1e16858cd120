from django.db import models
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.utils.text import slugify
from django.urls import reverse
from django.db.models.signals import pre_save
from django.dispatch import receiver

class UserManager(BaseUserManager):
    """Gerenciador personalizado para o modelo User com email como nome de usuário"""
    
    def create_user(self, email, password=None, **extra_fields):
        """Cria e salva um usuário com email e senha"""
        if not email:
            raise ValueError('O email é obrigatório')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """Cria e salva um superusuário com email e senha"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_verified', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser deve ter is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser deve ter is_superuser=True.')

        return self.create_user(email, password, **extra_fields)

class User(AbstractUser):
    """Modelo de usuário personalizado usando email como identificador principal"""
    
    email = models.EmailField(
        'endereço de email',
        unique=True,
        error_messages={
            'unique': "Já existe um usuário com este email.",
        }
    )
    is_verified = models.BooleanField(
        'verificado',
        default=False,
        help_text='Designa se o usuário verificou o email.'
    )
    slug = models.SlugField(
        'slug',
        max_length=100,
        unique=True,
        blank=True,
        help_text='Identificador único para URLs amigáveis'
    )
    
    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        verbose_name = 'usuário'
        verbose_name_plural = 'usuários'
        ordering = ['-date_joined']

    def __str__(self):
        return self.email

    def get_absolute_url(self):
        """Retorna a URL para acessar a página de perfil do usuário"""
        return reverse('accounts:user_profile', kwargs={'slug': self.slug})

    def get_full_name(self):
        """Retorna o nome completo do usuário"""
        return f"{self.first_name} {self.last_name}".strip()

    def generate_slug(self):
        """Gera um slug único baseado no email"""
        base_slug = slugify(self.email.split('@')[0])
        unique_slug = base_slug
        num = 1
        while User.objects.filter(slug=unique_slug).exists():
            unique_slug = f"{base_slug}-{num}"
            num += 1
        return unique_slug

@receiver(pre_save, sender=User)
def user_pre_save(sender, instance, **kwargs):
    """Signal para gerar slug automaticamente antes de salvar"""
    if not instance.slug:
        instance.slug = instance.generate_slug()