{% extends 'base.html' %}

{% block title %}Configuração Rápida de Email - {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-4">
                <h2 class="display-6">
                    <i class="fas fa-rocket text-primary me-2"></i>
                    Configuração Rápida de Email
                </h2>
                <p class="text-muted">Configure o envio de emails em poucos passos</p>
            </div>

            <!-- Status Atual -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Status Atual
                    </h5>
                </div>
                <div class="card-body">
                    {% if config_status.can_send %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Email já está configurado e funcionando!
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Email não está configurado. Configure agora para enviar códigos de redefinição de senha.
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Formulário de Configuração -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Configurar SMTP
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Configurações Pré-definidas -->
                        <div class="mb-4">
                            <h6>🚀 Configurações Rápidas:</h6>
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-danger w-100" onclick="fillGmailConfig()">
                                        <i class="fab fa-google me-2"></i>Gmail
                                    </button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-primary w-100" onclick="fillOutlookConfig()">
                                        <i class="fab fa-microsoft me-2"></i>Outlook
                                    </button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-success w-100" onclick="fillSendGridConfig()">
                                        <i class="fas fa-paper-plane me-2"></i>SendGrid
                                    </button>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Configurações Manuais -->
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="email_host" class="form-label">Servidor SMTP *</label>
                                <input type="text" class="form-control" id="email_host" name="email_host" 
                                       value="{{ current_config.EMAIL_HOST|default:'' }}"
                                       placeholder="smtp.gmail.com" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="email_port" class="form-label">Porta *</label>
                                <input type="number" class="form-control" id="email_port" name="email_port" 
                                       value="{{ current_config.EMAIL_PORT|default:'587' }}"
                                       placeholder="587" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email_user" class="form-label">Usuário/Email *</label>
                                <input type="email" class="form-control" id="email_user" name="email_user" 
                                       value="{{ current_config.EMAIL_HOST_USER|default:'' }}"
                                       placeholder="<EMAIL>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email_password" class="form-label">Senha *</label>
                                <input type="password" class="form-control" id="email_password" name="email_password" 
                                       placeholder="Sua senha ou senha de app" required>
                                <div class="form-text">
                                    Para Gmail, use uma <a href="https://support.google.com/accounts/answer/185833" target="_blank">senha de app</a>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_tls" name="use_tls" 
                                           {% if current_config.EMAIL_USE_TLS %}checked{% endif %}>
                                    <label class="form-check-label" for="use_tls">
                                        Usar TLS (recomendado)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_ssl" name="use_ssl"
                                           {% if current_config.EMAIL_USE_SSL %}checked{% endif %}>
                                    <label class="form-check-label" for="use_ssl">
                                        Usar SSL
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="from_email" class="form-label">Email Remetente *</label>
                            <input type="email" class="form-control" id="from_email" name="from_email" 
                                   value="{{ current_config.DEFAULT_FROM_EMAIL|default:'' }}"
                                   placeholder="<EMAIL>" required>
                            <div class="form-text">
                                Email que aparecerá como remetente das mensagens
                            </div>
                        </div>

                        <!-- Opções de Teste -->
                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6>🧪 Opções de Teste:</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test_after_save" name="test_after_save" checked>
                                    <label class="form-check-label" for="test_after_save">
                                        Testar conexão após salvar
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="send_test_email" name="send_test_email">
                                    <label class="form-check-label" for="send_test_email">
                                        Enviar email de teste para mim
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Botões -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'accounts:email_diagnostic' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Voltar
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Salvar Configurações
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Ajuda -->
            <div class="card mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Precisa de Ajuda?
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="helpAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gmail">
                                    <i class="fab fa-google me-2"></i>Como configurar Gmail
                                </button>
                            </h2>
                            <div id="gmail" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Ative a verificação em duas etapas na sua conta Google</li>
                                        <li>Gere uma <a href="https://support.google.com/accounts/answer/185833" target="_blank">senha de app</a></li>
                                        <li>Use estas configurações:
                                            <ul>
                                                <li><strong>Servidor:</strong> smtp.gmail.com</li>
                                                <li><strong>Porta:</strong> 587</li>
                                                <li><strong>TLS:</strong> Ativado</li>
                                                <li><strong>Usuário:</strong> <EMAIL></li>
                                                <li><strong>Senha:</strong> senha de app gerada</li>
                                            </ul>
                                        </li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#outlook">
                                    <i class="fab fa-microsoft me-2"></i>Como configurar Outlook
                                </button>
                            </h2>
                            <div id="outlook" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>Para Outlook.com/Hotmail:</strong></p>
                                    <ul>
                                        <li><strong>Servidor:</strong> smtp-mail.outlook.com</li>
                                        <li><strong>Porta:</strong> 587</li>
                                        <li><strong>TLS:</strong> Ativado</li>
                                        <li><strong>Usuário:</strong> <EMAIL></li>
                                        <li><strong>Senha:</strong> sua senha normal</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function fillGmailConfig() {
    document.getElementById('email_host').value = 'smtp.gmail.com';
    document.getElementById('email_port').value = '587';
    document.getElementById('use_tls').checked = true;
    document.getElementById('use_ssl').checked = false;
    
    // Focus no campo de usuário
    document.getElementById('email_user').focus();
}

function fillOutlookConfig() {
    document.getElementById('email_host').value = 'smtp-mail.outlook.com';
    document.getElementById('email_port').value = '587';
    document.getElementById('use_tls').checked = true;
    document.getElementById('use_ssl').checked = false;
    
    document.getElementById('email_user').focus();
}

function fillSendGridConfig() {
    document.getElementById('email_host').value = 'smtp.sendgrid.net';
    document.getElementById('email_port').value = '587';
    document.getElementById('email_user').value = 'apikey';
    document.getElementById('use_tls').checked = true;
    document.getElementById('use_ssl').checked = false;
    
    document.getElementById('email_password').focus();
}

// Validação em tempo real
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Salvando...';
        submitBtn.disabled = true;
    });
    
    // Sincronizar email do usuário com email remetente
    const emailUser = document.getElementById('email_user');
    const fromEmail = document.getElementById('from_email');
    
    emailUser.addEventListener('blur', function() {
        if (this.value && !fromEmail.value) {
            fromEmail.value = this.value;
        }
    });
});
</script>
{% endblock %}
