from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.core.exceptions import ValidationError
from django.contrib.auth.forms import UserCreationForm

User = get_user_model()

class UserCreateForm(UserCreationForm):
    """Formulário para criação de usuários"""
    
    email = forms.EmailField(
        label='E-mail',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    first_name = forms.CharField(
        label='Nome',
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nome'
        })
    )
    last_name = forms.CharField(
        label='Sobrenome',
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Sobrenome'
        })
    )
    username = forms.Char<PERSON>ield(
        label='Nome de usuário',
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'nome_usuario'
        })
    )
    is_active = forms.BooleanField(
        label='Ativo',
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    is_staff = forms.BooleanField(
        label='Staff',
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='Permite acesso ao admin'
    )
    is_superuser = forms.BooleanField(
        label='Superusuário',
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='Possui todas as permissões'
    )
    groups = forms.ModelMultipleChoiceField(
        label='Grupos',
        queryset=Group.objects.all(),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-check-input'
        })
    )

    class Meta:
        model = User
        fields = ('email', 'username', 'first_name', 'last_name', 
                 'password1', 'password2', 'is_active', 'is_staff', 
                 'is_superuser', 'groups')

    def clean_email(self):
        """Valida se o email não está em uso"""
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email__iexact=email).exists():
            raise ValidationError('Já existe um usuário com este e-mail.')
        return email

    def clean_username(self):
        """Valida se o username não está em uso"""
        username = self.cleaned_data.get('username')
        if username and User.objects.filter(username__iexact=username).exists():
            raise ValidationError('Já existe um usuário com este nome de usuário.')
        return username


class UserUpdateForm(forms.ModelForm):
    """Formulário para atualização de usuários"""
    
    email = forms.EmailField(
        label='E-mail',
        widget=forms.EmailInput(attrs={
            'class': 'form-control'
        })
    )
    first_name = forms.CharField(
        label='Nome',
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control'
        })
    )
    last_name = forms.CharField(
        label='Sobrenome',
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control'
        })
    )
    username = forms.CharField(
        label='Nome de usuário',
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control'
        })
    )
    is_active = forms.BooleanField(
        label='Ativo',
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    is_staff = forms.BooleanField(
        label='Staff',
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    is_superuser = forms.BooleanField(
        label='Superusuário',
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    groups = forms.ModelMultipleChoiceField(
        label='Grupos',
        queryset=Group.objects.all(),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-check-input'
        })
    )

    class Meta:
        model = User
        fields = ('email', 'username', 'first_name', 'last_name', 
                 'is_active', 'is_staff', 'is_superuser', 'groups')

    def __init__(self, *args, **kwargs):
        self.instance_id = kwargs.get('instance').id if kwargs.get('instance') else None
        super().__init__(*args, **kwargs)

    def clean_email(self):
        """Valida se o email não está em uso por outro usuário"""
        email = self.cleaned_data.get('email')
        if email:
            existing = User.objects.filter(email__iexact=email).exclude(id=self.instance_id)
            if existing.exists():
                raise ValidationError('Já existe um usuário com este e-mail.')
        return email

    def clean_username(self):
        """Valida se o username não está em uso por outro usuário"""
        username = self.cleaned_data.get('username')
        if username:
            existing = User.objects.filter(username__iexact=username).exclude(id=self.instance_id)
            if existing.exists():
                raise ValidationError('Já existe um usuário com este nome de usuário.')
        return username


class UserPermissionForm(forms.Form):
    """Formulário para gerenciar permissões de usuário"""
    
    permissions = forms.ModelMultipleChoiceField(
        label='Permissões',
        queryset=Permission.objects.all().select_related('content_type'),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-check-input'
        })
    )

    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        
        if user:
            # Define permissões atuais como selecionadas
            self.fields['permissions'].initial = user.user_permissions.all()
        
        # Organiza permissões por app
        permissions = Permission.objects.all().select_related('content_type').order_by(
            'content_type__app_label', 'content_type__model', 'codename'
        )
        self.fields['permissions'].queryset = permissions


class UserSearchForm(forms.Form):
    """Formulário para busca de usuários"""
    
    query = forms.CharField(
        label='Buscar',
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nome, email ou username...'
        })
    )
    is_active = forms.ChoiceField(
        label='Status',
        choices=[
            ('', 'Todos'),
            ('true', 'Ativos'),
            ('false', 'Inativos')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
    is_staff = forms.ChoiceField(
        label='Staff',
        choices=[
            ('', 'Todos'),
            ('true', 'Staff'),
            ('false', 'Não Staff')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
    is_superuser = forms.ChoiceField(
        label='Superusuário',
        choices=[
            ('', 'Todos'),
            ('true', 'Superusuários'),
            ('false', 'Usuários normais')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
