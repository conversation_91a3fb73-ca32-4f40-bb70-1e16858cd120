# 🔧 App Config - Sistema de Gerenciamento de Usuários e Permissões

## 📋 **VISÃO GERAL**

O app `config` é um sistema completo para gerenciamento de usuários, permissões, grupos e configurações do sistema. Implementa arquitetura limpa com Services, Repositories e Interfaces.

---

## 🏗️ **ARQUITETURA**

### **Estrutura de Diretórios**
```
apps/config/
├── interfaces/          # Contratos (Interfaces)
│   ├── services.py      # IUserManagementService, IPermissionManagementService, etc.
│   └── repositories.py  # IUserRepository, IPermissionRepository, etc.
├── services/            # Regras de negócio
│   ├── user_management_service.py
│   ├── permission_management_service.py
│   └── system_config_service.py
├── repositories/        # Acesso a dados
│   ├── user_repository.py
│   ├── permission_repository.py
│   └── config_repository.py
├── forms/               # Formulários
│   └── user_forms.py
├── management/          # Comandos personalizados
│   └── commands/
│       ├── setup_permissions.py
│       └── create_admin_user.py
├── models.py           # Models (SystemConfiguration, UserActivityLog)
├── views.py            # Views para CRUD
├── urls.py             # URLs do app
├── admin.py            # Configuração do Django Admin
└── signals.py          # Signals para auditoria
```

---

## 🔧 **FUNCIONALIDADES**

### **1. Gerenciamento de Usuários**
- ✅ **CRUD completo** de usuários
- ✅ **Busca e filtros** avançados
- ✅ **Validações** de email e username únicos
- ✅ **Controle de status** (ativo, staff, superuser)
- ✅ **Auditoria** de todas as operações

### **2. Gerenciamento de Permissões**
- ✅ **Atribuição/remoção** de permissões individuais
- ✅ **Gerenciamento de grupos** de usuários
- ✅ **Visualização** de permissões efetivas
- ✅ **Hierarquia** de permissões por grupos

### **3. Configurações do Sistema**
- ✅ **Armazenamento** de configurações chave-valor
- ✅ **Suporte a JSON** para configurações complexas
- ✅ **Versionamento** e auditoria de mudanças
- ✅ **Interface** para gerenciamento

### **4. Auditoria e Logs**
- ✅ **Log automático** de todas as ações
- ✅ **Rastreamento** de IP e User-Agent
- ✅ **Dados extras** em formato JSON
- ✅ **Relatórios** de atividade

---

## 🚀 **INSTALAÇÃO E CONFIGURAÇÃO**

### **1. Configuração Inicial**
```bash
# 1. Fazer migrações
python manage.py migrate

# 2. Configurar permissões e grupos
python manage.py setup_permissions

# 3. Criar usuário administrador
python manage.py create_admin_user
```

### **2. Configuração de Permissões**
```bash
# Configurar grupos padrão
python manage.py setup_permissions

# Recriar grupos (remove existentes)
python manage.py setup_permissions --reset
```

### **3. Criar Usuário Admin**
```bash
# Modo interativo
python manage.py create_admin_user

# Modo não-interativo
python manage.py create_admin_user \
    --email <EMAIL> \
    --username admin \
    --password minhasenha123 \
    --no-input
```

---

## 📚 **COMO USAR**

### **1. Acessar o Dashboard**
```
URL: /config/
Permissão: auth.view_user
```

### **2. Gerenciar Usuários**
```
Listar:  /config/usuarios/
Criar:   /config/usuarios/criar/
Editar:  /config/usuarios/<id>/editar/
Deletar: /config/usuarios/<id>/deletar/
```

### **3. Usar os Services**

#### **UserManagementService**
```python
from apps.config.services.user_management_service import UserManagementService
from apps.config.repositories.user_repository import DjangoUserRepository
from apps.config.services.system_config_service import AuditLogService
from apps.config.repositories.config_repository import DjangoAuditLogRepository

# Inicializar services
audit_service = AuditLogService(DjangoAuditLogRepository())
user_service = UserManagementService(DjangoUserRepository(), audit_service)

# Criar usuário
user_data = {
    'email': '<EMAIL>',
    'username': 'novousuario',
    'password': 'senha123',
    'first_name': 'Novo',
    'last_name': 'Usuário',
    'is_active': True
}
user = user_service.create_user(user_data, request.user)

# Atualizar usuário
update_data = {'first_name': 'Nome Atualizado'}
user = user_service.update_user(user.id, update_data, request.user)

# Buscar usuários
users = user_service.search_users('termo de busca')
```

#### **PermissionManagementService**
```python
from apps.config.services.permission_management_service import PermissionManagementService

# Inicializar service
permission_service = PermissionManagementService(
    user_repository=DjangoUserRepository(),
    permission_repository=DjangoPermissionRepository(),
    group_repository=DjangoGroupRepository(),
    audit_service=audit_service
)

# Atribuir permissão
permission_service.assign_permission_to_user(
    user_id=1, 
    permission_id=10, 
    assigned_by=request.user
)

# Atribuir grupo
permission_service.assign_group_to_user(
    user_id=1, 
    group_id=2, 
    assigned_by=request.user
)

# Obter permissões do usuário
permissions = permission_service.get_user_permissions(user_id=1)
```

---

## 🔒 **PERMISSÕES E GRUPOS**

### **Grupos Padrão**
| Grupo | Descrição | Permissões |
|-------|-----------|------------|
| **Administradores** | Acesso total | Todas as permissões |
| **Gerentes** | Gerenciamento de usuários | CRUD de usuários e grupos |
| **Operadores** | Operações básicas | Visualizar e editar usuários |
| **Usuários** | Acesso básico | Visualizar próprio perfil |

### **Permissões Customizadas**
- `can_manage_system_config` - Gerenciar configurações do sistema
- `can_view_audit_logs` - Visualizar logs de auditoria
- `can_export_data` - Exportar dados do sistema

---

## 📊 **MODELS**

### **SystemConfiguration**
```python
# Armazenar configurações do sistema
config = SystemConfiguration.objects.create(
    key='max_login_attempts',
    value='5',
    description='Máximo de tentativas de login',
    updated_by=user
)

# Usar como JSON
config.set_value_from_dict({'attempts': 5, 'timeout': 300})
data = config.get_value_as_json()
```

### **UserActivityLog**
```python
# Logs são criados automaticamente via signals
# Buscar logs de um usuário
logs = UserActivityLog.objects.filter(user=user).order_by('-created_at')

# Buscar logs por ação
logs = UserActivityLog.objects.filter(action='CREATE')
```

---

## 🔍 **AUDITORIA**

### **Ações Rastreadas**
- `CREATE` - Criação de usuário
- `UPDATE` - Atualização de usuário
- `DELETE` - Exclusão de usuário
- `LOGIN` - Login no sistema
- `LOGOUT` - Logout do sistema
- `PASSWORD_CHANGE` - Alteração de senha
- `PERMISSION_CHANGE` - Alteração de permissão
- `GROUP_CHANGE` - Alteração de grupo

### **Dados Capturados**
- Usuário que executou a ação
- Usuário alvo (se aplicável)
- Descrição da ação
- Endereço IP
- User-Agent
- Dados extras em JSON
- Timestamp

---

## 🛠️ **DESENVOLVIMENTO**

### **Adicionar Nova Funcionalidade**
1. **Criar interface** em `interfaces/`
2. **Implementar repository** em `repositories/`
3. **Criar service** em `services/`
4. **Adicionar view** em `views.py`
5. **Configurar URL** em `urls.py`
6. **Criar formulário** em `forms/`

### **Exemplo: Novo Service**
```python
# interfaces/services.py
class INewService(ABC):
    @abstractmethod
    def new_method(self): pass

# services/new_service.py
class NewService(INewService):
    def new_method(self):
        # Implementação
        pass
```

---

## 🧪 **TESTES**

```bash
# Executar testes do app config
python manage.py test apps.config

# Executar testes específicos
python manage.py test apps.config.tests.test_services
```

---

## 📈 **MONITORAMENTO**

### **Métricas Importantes**
- Total de usuários ativos
- Logins por dia/semana
- Ações administrativas
- Tentativas de acesso negado
- Configurações alteradas

### **Logs para Monitorar**
- Criação de usuários administradores
- Alterações de permissões críticas
- Acessos fora do horário comercial
- Múltiplas tentativas de login

---

**Sistema implementado com sucesso! 🎉**
